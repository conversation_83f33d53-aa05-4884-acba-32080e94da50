<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V2EX Polish 悬浮效果测试</title>
    <link rel="stylesheet" href="extension/css/v2ex-theme-var.css">
    <link rel="stylesheet" href="extension/css/v2ex-theme-dark.css">
    <link rel="stylesheet" href="extension/css/v2ex-layout-improvements.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: system-ui, sans-serif;
            background: var(--v2p-color-background);
            color: var(--v2p-color-foreground);
            transition: all 0.3s ease;
        }
        
        /* 模拟V2EX的顶部选项卡容器 */
        #Tabs {
            background: var(--v2p-color-bg-content);
            border: 1px solid var(--v2p-color-border);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        #Tabs .content {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .test-section {
            background: var(--v2p-color-bg-content);
            border: 1px solid var(--v2p-color-border);
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .test-section h2 {
            color: var(--v2p-color-foreground);
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .test-section h3 {
            color: var(--v2p-color-foreground);
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .test-section p {
            color: var(--v2p-color-font-secondary);
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .hover-instruction {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #3b82f6;
            font-weight: 500;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            padding: 15px;
            border: 1px solid var(--v2p-color-border);
            border-radius: 8px;
            text-align: center;
        }
        
        .comparison-item.correct {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
        }
        
        .comparison-item.incorrect {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.3);
        }
        
        .shape-demo {
            width: 100px;
            height: 40px;
            margin: 10px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--v2p-color-bg-reply);
            border: 1px solid var(--v2p-color-border);
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .shape-demo.ellipse {
            border-radius: 12px;
        }
        
        .shape-demo.rectangle {
            border-radius: 3px;
        }
        
        .shape-demo:hover {
            background: var(--v2p-color-button-background-hover);
            border-color: var(--v2p-color-border-darker);
        }
        
        .theme-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 12px 24px;
            background: var(--v2p-color-button-background);
            color: var(--v2p-color-button-foreground);
            border: 1px solid var(--v2p-color-border);
            border-radius: 8px;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.2s ease;
            font-weight: 500;
        }
        
        .theme-toggle:hover {
            background: var(--v2p-color-button-background-hover);
            transform: translateY(-2px);
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            margin: 10px 0;
        }
        
        .status-indicator.success {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        
        .status-indicator.warning {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.2);
        }
    </style>
</head>
<body class="v2p-theme-dark-default" id="Wrapper">
    <div class="test-section">
        <h2>🔍 V2EX Polish 悬浮效果测试</h2>
        <p>这个页面专门用于测试顶层选项卡的悬浮效果是否保持椭圆形。</p>
        
        <div class="hover-instruction">
            📌 <strong>测试说明：</strong>将鼠标悬浮在下方的选项卡上，观察形状是否保持椭圆形
        </div>
    </div>

    <!-- 模拟V2EX的顶层选项卡 -->
    <div id="Tabs">
        <h3>实际选项卡测试</h3>
        <div class="content">
            <a href="#" class="tab">技术</a>
            <a href="#" class="tab tab_current">创意</a>
            <a href="#" class="tab">好玩</a>
            <a href="#" class="tab">Apple</a>
            <a href="#" class="tab">酷工作</a>
            <a href="#" class="tab">交易</a>
            <a href="#" class="tab">城市</a>
            <a href="#" class="tab">问与答</a>
            <a href="#" class="tab">最热</a>
            <a href="#" class="tab">全部</a>
            <a href="#" class="tab">R2</a>
        </div>
    </div>

    <div class="test-section">
        <h3>🎯 期望效果对比</h3>
        <p>下面展示了正确和错误的悬浮效果对比：</p>
        
        <div class="comparison-grid">
            <div class="comparison-item correct">
                <h4 style="color: #10b981; margin-top: 0;">✅ 正确效果</h4>
                <div class="shape-demo ellipse">椭圆形</div>
                <p style="font-size: 12px; margin-bottom: 0;">悬浮时保持椭圆形</p>
            </div>
            
            <div class="comparison-item incorrect">
                <h4 style="color: #ef4444; margin-top: 0;">❌ 错误效果</h4>
                <div class="shape-demo rectangle">矩形</div>
                <p style="font-size: 12px; margin-bottom: 0;">悬浮时变成矩形</p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>🔧 修复措施</h3>
        <p>为了确保悬浮效果保持椭圆形，我们采用了以下技术措施：</p>
        <ul>
            <li><strong>多重选择器覆盖：</strong>使用多个具体的CSS选择器确保覆盖V2EX原有样式</li>
            <li><strong>!important 声明：</strong>强制应用椭圆形边框半径</li>
            <li><strong>状态覆盖：</strong>覆盖所有可能的状态（hover、focus、active、visited）</li>
            <li><strong>属性选择器：</strong>使用 [class*="tab"] 确保匹配所有相关元素</li>
        </ul>
        
        <div class="status-indicator success">
            ✅ 已应用强化的CSS覆盖规则
        </div>
        
        <div class="status-indicator warning">
            ⚠️ 如果仍有问题，可能需要检查浏览器缓存或CSS加载顺序
        </div>
    </div>

    <div class="test-section">
        <h3>📋 测试清单</h3>
        <p>请逐一验证以下项目：</p>
        <ul>
            <li>□ 默认状态下选项卡为椭圆形</li>
            <li>□ 鼠标悬浮时选项卡保持椭圆形</li>
            <li>□ 当前激活的选项卡为椭圆形</li>
            <li>□ 悬浮激活选项卡时保持椭圆形</li>
            <li>□ 选项卡间距适中（2px）</li>
            <li>□ 悬浮时有平滑的过渡动画</li>
        </ul>
    </div>

    <button class="theme-toggle" onclick="toggleTheme()">
        切换主题模式
    </button>

    <script>
        // 主题切换功能
        function toggleTheme() {
            const body = document.body;
            const wrapper = document.getElementById('Wrapper');
            const isNight = body.classList.contains('v2p-theme-dark-default');
            
            if (isNight) {
                body.classList.remove('v2p-theme-dark-default');
                body.classList.add('v2p-theme-light-default');
                wrapper.classList.remove('Night');
            } else {
                body.classList.remove('v2p-theme-light-default');
                body.classList.add('v2p-theme-dark-default');
                wrapper.classList.add('Night');
            }
        }
        
        // 动态检测悬浮效果
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('#Tabs .tab');
            
            tabs.forEach(tab => {
                tab.addEventListener('mouseenter', function() {
                    const computedStyle = window.getComputedStyle(this);
                    const borderRadius = computedStyle.borderRadius;
                    
                    console.log('Tab hover border-radius:', borderRadius);
                    
                    // 检查是否为椭圆形（12px）
                    if (borderRadius.includes('12px')) {
                        console.log('✅ 椭圆形悬浮效果正确');
                    } else {
                        console.log('❌ 悬浮效果不是椭圆形:', borderRadius);
                    }
                });
            });
        });
    </script>
</body>
</html>
