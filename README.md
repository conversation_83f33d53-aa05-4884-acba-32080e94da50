# V2EX Polish - 体验更现代化的 V2EX

![V2EX Polish 宣传封面图](https://cdn.jsdelivr.net/gh/Codennnn/static/preview/V2EX_Polish.jpg)

一款专为 V2EX 用户设计的轻量浏览器插件，提供了丰富的扩展功能，让原生页面焕然一新！✨

[![Badge CWS Version]][Link CWS] ·
[![Badge CWS Downloads]][Link CWS] ·
[![Badge CWS Rating]][Link CWS Rating] ·
[![Badge CWS Rating Count]][Link CWS Rating]

## 安装使用

- Chrome、Edge、Arc 用户请在 [Chrome 商店中安装][Link CWS]
- Firefox 用户[在此下载安装](https://addons.mozilla.org/zh-CN/firefox/addon/v2ex-polish/)
- Safari 用户参见[安装教程](./website/src/content/docs/use-in-safari.md)
- [油猴脚本](https://greasyfork.org/zh-CN/scripts/459848-v2ex-polish-%E4%BD%93%E9%AA%8C%E6%9B%B4%E7%8E%B0%E4%BB%A3%E5%8C%96%E7%9A%84-v2ex)（仅支持部分功能，文档后面介绍了功能差异）

> [!IMPORTANT]
> 使用其他类似的脚本或插件可能会导致冲突，如果在使用过程中发现网页内容有误，建议关闭其他插件以排查问题。

## 扩展功能

### 特色功能：

- 🪄 界面美化：UI 设计更现代化，为你带来愉悦的视觉体验。

- 📥 评论回复嵌套层级：主题下的评论回复支持层级展示，更轻松地追踪和回复他人的评论。

- 🔥 热门回复展示：自动筛选出受欢迎的回复，热评先睹为快。

- 😀 表情回复支持：评论输入框可以选择并插入表情，让回复更加生动和有趣。

- 📃 长回复优化：智能折叠冗长回复，并可一键展开阅读完整内容。

- 📰 内置主题列表：无需打开网页，在插件内即可快速获取最热、最新的主题列表和消息通知。

- 📝 便捷回复操作：预览回复、文字转 Base64、上传图片。

<details>
  <summary>点击发现更多功能👇</summary>

---

- 自动领取每日签到奖励。

- 设置用户标签，快速标记各类用户。

- 添加用户信息卡片，快捷查看用户信息。

- 支持自动跟随系统切换浅色/深色主题。

- 支持解析页面中所有 Base64 编码的内容。

- 支持预加载多页回复，完美呈现嵌套回复。

- 支持水平分区阅读主题内容。

- “稍后阅读”：添加感兴趣的主题，方便日后浏览。

- 无需访问主题页面，在主题列表中即可预览内容。

- 一键生成主题分享图片。

- 支持备份个人配置，方便跨设备同步。

- 翻页后自动跳转到回复区。

</details>

### 部分功能效果展示

<table>
<tr>
<td>视觉友好的界面设计：</td>
<td>丰富的个性化设置：</td>
</tr>
<tr>
<td><img src=https://github.com/coolpace/V2EX_Polish/assets/47730755/670411b9-039e-4dbb-97c9-b6c1d83d86a7 width=600/></td>
<td><img src=https://github.com/coolpace/V2EX_Polish/assets/47730755/1364ee37-cba6-4a84-8255-17e90140f642 width=600/></td>
</tr>
<tr>
<td>在回复中快速上传图片：</td>
<td>实时预览回复内容：</td>
</tr>
<tr>
<td><img src=https://i.imgur.com/XIOFQVS.gif width=600/></td>
<td><img src=https://i.imgur.com/FYSZ0n5.gif width=600/></td>
</tr>
<tr>
<td>设置用户的标签，标记用户：</td>
<td>生成主题分享图片：</td>
</tr>
<tr>
<td><img src=https://i.imgur.com/YNFJeFv.gif width=600/></td>
<td><img src=https://i.imgur.com/jpHLxP2.gif width=600/></td>
</tr>
<tr>
<td>隐藏回复中 @ 用户的名称：</td>
<td>回复中插入热门流行表情：</td>
</tr>
<tr>
<td><img src=https://i.imgur.com/jB8s6kd.gif width=600/></td>
<td><img src=https://i.imgur.com/5Xkwk4L.gif width=600/></td>
</tr>
</table>

## 为什么选择 V2EX Polish？

尽管市面上早已存在众多增强 v2ex.com 的[脚本](https://greasyfork.org/zh-CN/scripts/by-site/v2ex.com)和[插件][Link CWS Search V2EX]，但它们带来的体验良莠不齐，且大多数已经停止更新。

V2EX Polish 的目标是提供一个更加完善的插件，并保证持续更新，快速响应 V2EX 用户的需求。我们志在打造最高质量的 V2EX 扩展，带给大家最佳的体验。

## 如何帮助我们

作为开发者，创造对他人有用的东西始终是我们的热情所在，这个项目也不例外。我们投入了大量的时间和精力，致力于为 V2EX 用户带来更好的体验。因此，如果我们的项目帮助了你，欢迎你为我们的项目：

- 点个 Star ⭐️ 或分享给他人，让更多的人知道我们的存在。
- [赞赏作者][Link Donation]，这会激励作者投入更多的时间完善项目。
- 提供反馈，帮助我们改进。
- 改善代码，请阅读我们的[代码贡献指南](./.github/CONTRIBUTING.md)。

## 常见问题

<details>
<summary>使用油猴脚本和浏览器扩展有什么区别？</summary>

油猴脚本不支持：

- 所有个性化设置
- 右键功能菜单
- 用户标签设置

浏览器扩展支持全部功能，并且经过了更多的测试。为了达到最佳的功能体验，我们更推荐你安装扩展。担心扩展的体积太大？请放心，本扩展的安装体积还不到 **0.2 MB**⚡！我们十分关注扩展的体积和性能，努力减少资源占用。

</details>

<details>
<summary>为什么我的页面内容有误、样式异常？</summary>

如果你使用了其他与 V2EX 相关的插件，那么很可能会引发功能冲突，从而导致页面异常，建议关闭其他插件以排查问题。

</details>

<details>
<summary>为什么有的「楼中楼」回复的楼层不正确？</summary>

由于 V2EX 的原回复并没有记录回复的楼层，本扩展只能根据被回复的用户去寻找此用户的最近一条回复，然后嵌入到这后面去，这种方法并不能保证正确识别用户真正要回复的是哪一个楼层。

</details>

<details>
<summary>为什么需要设置「个人访问令牌（PAT）」？</summary>

PAT 并不是必需的，只有当你想要使用诸如：主题内容预览、获取消息通知等功能时才需要设置，它是用来访问 [V2EX 开放 API](https://www.v2ex.com/help/api) 的。如果你还没有，请前往[这里创建](https://www.v2ex.com/settings/tokens)。

</details>

## 问题反馈

我们需要你的建议和反馈，以持续完善 V2EX Polish。如果在使用中遇到任何问题，都可以[在这里](https://github.com/coolpace/V2EX_Polish/discussions/1?sort=new)提出。你也可以加入我们的 [Telegram 群组](https://t.me/+zH9GxA2DYLtjYjhl)向我们快速反馈。

## V2EX 相关主题

- [V2EX 超强浏览器扩展：体验更先进的 V2EX](https://www.v2ex.com/t/930155)
- [V2EX Polish 大量功能更新，即刻体验更好用的 V2EX](https://www.v2ex.com/t/935916)
- [关于 V2EX Polish 意外从 Chrome 应用商店下架的说明](https://www.v2ex.com/t/940580)
- [V2EX Polish 在 5 月份更新了什么？](https://www.v2ex.com/t/942786)
- [V2EX 插件更新：预览回复、生成主题分享图片、隐藏回复用户名称、更多好玩的回复表情！](https://www.v2ex.com/t/977271)
- [V2EX 插件更新：主题水平布局、选项页美化、更多功能...](https://www.v2ex.com/t/1007017)

**喜欢我们的扩展吗？请在[应用商店][Link CWS]给我们好评！🥰**

## 赞赏支持

如果这个插件帮助你节省了时间，让你的生活更加愉快，可以给开发者一点小小的赞赏，这会帮助插件更持续地发展。对于你们的大方支持，我们感慨万分！

![赞赏码](./assets/appreciation-code.png)

<!--------[Badges]-------->

[Badge CWS Version]: https://img.shields.io/chrome-web-store/v/onnepejgdiojhiflfoemillegpgpabdm.svg?style=flat&colorA=232323&colorB=232323&label=最新版本&logo=hackthebox&logoColor=eeeeee
[Badge CWS Downloads]: https://img.shields.io/chrome-web-store/users/onnepejgdiojhiflfoemillegpgpabdm.svg?style=flat&colorA=232323&colorB=232323&label=用户量
[Badge CWS Rating]: https://img.shields.io/chrome-web-store/rating/onnepejgdiojhiflfoemillegpgpabdm.svg?style=flat&colorA=232323&colorB=232323&label=评分
[Badge CWS Rating Count]: https://img.shields.io/chrome-web-store/rating-count/onnepejgdiojhiflfoemillegpgpabdm.svg?style=flat&colorA=232323&colorB=232323&label=评价数

<!--------[Internal]-------->

[Link CWS]: https://chromewebstore.google.com/detail/v2ex-polish/onnepejgdiojhiflfoemillegpgpabdm
[Link CWS Search V2EX]: https://chromewebstore.google.com/search/V2EX?hl=zh-CN
[Link CWS Rating]: https://chromewebstore.google.com/detail/v2ex-polish/onnepejgdiojhiflfoemillegpgpabdm/reviews?hl=zh-CN
[Link Donation]: https://www.v2p.app/support
