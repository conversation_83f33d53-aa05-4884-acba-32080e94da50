---
title: 在 Safari 中使用 V2EX Polish
date: 2024-01-12
author:
  name: <PERSON><PERSON><PERSON>
  avatar: https://avatars.githubusercontent.com/u/47730755?v=4
  link: https://github.com/Codennnn
---

# 在 Safari 中使用 V2EX Polish

如果你想在 Safari 中使用 V2EX Polish，虽然目前不支持 Safari 扩展，但你仍然可以通过安装脚本的方式来实现。

## 安装 UserScripts

首先，你需要在 App Store 中搜索并下载 [UserScripts](https://apps.apple.com/us/app/userscripts/id1463298887)。这是一个免费的 Safari 扩展，它允许你在 Safari 中运行用户脚本，功能上类似于 Tampermonkey。

![UserScripts in App Store](https://i.imgur.com/AeN8BnK.png)

## 添加脚本

安装完 UserScripts 后，你可以从 Greasy Fork 获取 [V2EX Polish 的脚本代码](https://greasyfork.org/zh-CN/scripts/459848-v2ex-polish-%E4%BD%93%E9%AA%8C%E6%9B%B4%E7%8E%B0%E4%BB%A3%E5%8C%96%E7%9A%84-v2ex/code)。

打开 UserScripts，将脚本代码粘贴进去。确保代码正确无误后，保存脚本。

![V2EX Polish 的脚本代码](https://i.imgur.com/NITKAa7.png)

## 启用脚本

在 UserScripts 的脚本列表中，找到你刚刚添加的 V2EX Polish 脚本，并确保它已被启用。启用后，你可以访问 V2EX 看到脚本带来的效果。

![V2EX Polish 的效果](https://i.imgur.com/fAv0xsH.png)
