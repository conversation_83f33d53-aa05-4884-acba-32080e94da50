## 1.10.x

### 优化改进

- 优化「主题预览」功能。
- 优化 modal 点击区域意外关闭的问题。

---

## 1.9.x

### 新功能

- 支持在主题列表中直接屏蔽主题。
- 增加图片分享操作按钮。
- 增加「近期热议主题」板块。

### 优化改进

- 新增 `s.v2ex.com` 作为匹配子域名。
- 优化渲染用户标签的逻辑。
- 修复无法自动隐藏回复时间的问题。
- 修复无法正确打开 Chrome 侧边栏的问题。
- 修复 Popup 主题列表内容转义的问题。
- 修复导致登录页无法正常跳转的问题。

---

## 1.8.x

### 新功能

- 主题页支持水平方向划分内容区和回复区。
- 选项页中新增用户标签管理。
- Popup 中支持查看未读消息的数量。
- 支持自动备份和同步配置数据。
- 自动标注 30 天内注册的用户。

### 优化改进

- 回复内容中默认不显示 @ 提及的用户名。
- 默认不显示回复时间。
- 新增更多对 v2ex.com 子域名的匹配。
- 选项页支持自动跟随系统切换主题颜色。
- 优化了选项页的响应式设计。
- 整合了⌈热门回复⌋和⌈最近回复⌋的展示，新增⌈题主回复⌋。

---

## 1.7.x

### 新功能

- 添加主题回复内容预览。
- 新增生成分享图片功能。
- 支持隐藏回复中用户名称。
- 回复表情添加「bilibili / 小红书」流行表情。

### 优化改进

- 美化主题编辑页。

---

## 1.5.x - 1.6.x

### 新功能

- Popup 支持深色模式的颜色主题。
- 添加了对 `cn.v2ex.com` 域名的识别。
- 支持设置「用户标签」的展示形式。
- 添加关闭「楼中楼」功能的选项。
- 支持预览热议主题。

### 优化改进

- 使用 [lucide icon](https://lucide.dev/icons/) 作为新的展示图标。
- 支持在预览内容中展示附言。
- 用户标签改进：支持单独给题主（OP）设置标签。

### 优化改进

- 使用 [lucide icon](https://lucide.dev/icons/) 作为新的展示图标。
- 支持在预览内容中展示附言。

---

## 1.4.x

### 新功能

- 新增“稍后阅读”功能。
- 支持预加载多页回复，让嵌套回复更完美。
- 创建主题时也支持上传图片。

### 优化改进

- 修复感谢功能状态与提示不同步的问题。
- 修复 LOL 节点下文本颜色不兼容的问题。
- 完善用户配置的备份与同步。
- 回复时自动添加楼层号。
- 收藏主题时无须刷新页面。

---

## 1.3.x

### 新功能

- 新增同步和备份个人配置功能。
- 新增设置用户标签功能。
- 新增 SOV2EX 作为搜索选项。

### 优化改进

- 优化用户信息卡片：鼠标悬浮头像弹出。
- 浅色和深色模式现在可以自动跟随系统切换。
- 支持设置是否自动折叠长回复。
- 调整部分样式细节。

---

## 1.2.x

### 新功能和改进

- 主题样式优化。
- 便捷回复工具箱：文字转 Base64、上传图片。
- 支持更多个性化配置。
- 支持油猴脚本。
