---
title: 迭代更新：主题水平布局、选项页美化、更多功能...
date: 2024-01-05
author:
  name: <PERSON><PERSON><PERSON>
  avatar: https://avatars.githubusercontent.com/u/47730755?v=4
  link: https://github.com/Codennnn
---

好久不见，V2EX 的朋友们！

自上次发布了令人兴奋的[功能更新](https://www.v2ex.com/t/977271)以来，时间已过去了三个月。2024 年伊始，V2EX Polish 踏着新春的脚步，带着新功能又和大家见面啦！

## 更新亮点

- 主题页支持水平方向划分内容区和回复区。
- 插件选项页界面美化。
- 选项页中新增用户标签管理。
- Popup 中支持查看未读消息的数量。
- 回复内容中默认不显示 @ 提及的用户名。
- 默认不显示回复时间。

## 详细介绍

### ⊙ 主题页支持水平方向划分内容区和回复区。

这绝对是一个实用的功能。它有效地利用了页面的水平空间，让主题内容与回复同屏展示，告别了来回滚动查阅内容和回复的恼人体验。

![主题页水平布局](https://i.imgur.com/2blSYZt.png)

### ⊙ 插件选项页界面美化

为了适应 V2EX Polish 日益复杂的选项配置，我们重新设计了选项页，现在它看上去更美观了。

![选项页界面美化](https://i.imgur.com/EhpTZ4v.png)

### ⊙ 选项页中新增用户标签管理。

在去年四月份的[更新](https://www.v2ex.com/t/935916)中，我们就已经支持设置用户标签，但一直缺少管理标签的界面。现在，它终于来了，你可以在选项页中方便地管理你所设置的标签。

![管理用户标签](https://i.imgur.com/GpNy24a.png)

### ⊙ Popup 中支持查看未读消息的数量。

你可以在不访问网页的情况下，快速查看是否有未读消息。

![展示未读消息数量](https://i.imgur.com/fisZVqI.png)

### ⊙ 回复内容中默认不显示 @ 提及的用户名。

鉴于有了“楼中楼”功能，回复内容中提及用户的用户名显得没有必要。为了让界面显得简洁，我们默认隐藏了用户名，不过你可以在选项页中关闭这个功能。

### ⊙ 默认不显示回复时间。

同样的，为了回复区的简洁利落，我们在回复区域隐藏了回复时间。只有当鼠标浮动到某个回复上时，才会显示该回复的时间。

## 如果你第一次听说 V2EX Polish

V2EX Polish 是一款开源的浏览器插件，为改善 V2EX 落后的浏览体验而诞生。这款轻量级但功能丰富的插件提供了丰富的功能扩展，让你在浏览 V2EX 时更加得心应手。

- 查看官网了解更多： [https://www.v2p.app](https://www.v2p.app)
- 开源地址： [https://github.com/coolpace/V2EX_Polish](https://github.com/coolpace/V2EX_Polish)
- 安装地址： [https://chromewebstore.google.com/detail/v2ex-polish/onnepejgdiojhiflfoemillegpgpabdm](https://chromewebstore.google.com/detail/v2ex-polish/onnepejgdiojhiflfoemillegpgpabdm)

## 赞赏支持

如果这个插件改善了你浏览 V2EX 的体验，让你的生活更加愉快，可以给开发者一点小小的赞赏，这会帮助插件更持续地发展。对于你们的大方支持，我们感慨万分！

![微信赞赏码](https://i.imgur.com/AHmfQyK.jpg)
