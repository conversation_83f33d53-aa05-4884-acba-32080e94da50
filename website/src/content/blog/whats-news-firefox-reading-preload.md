---
title: 迭代更新：支持 Firefox、稍后阅读、预加载多页回复
date: 2023-05-25
author:
  name: <PERSON><PERSON><PERSON>
  avatar: https://avatars.githubusercontent.com/u/47730755?v=4
  link: https://github.com/Codennnn
---

## 更新概览

### 新功能

- 支持 Firefox。
- 增加 SOV2EX 作为搜索入口。
- 支持主题颜色自动跟随系统选择。
- 新增“稍后阅读”功能，让主题“飞一会”。
- 支持预加载多页回复，让嵌套回复更完美。

### 优化改进

- 优化用户信息卡片：鼠标悬浮头像弹出.
- 回复时自动添加楼层号.
- 完善用户配置的备份与同步.

## 新功能亮点介绍

**新增“稍后阅读”功能，让主题“飞一会”**

当你遇到感兴趣的主题时，你可能想把它囤起来等有空再看，或想让评论区发酵一会。
以前你可能会“收藏主题”，现在，你有了更方便的选择：稍后阅读。在主题页的空白处右键菜单，选择「添加进稍后阅读」，然后点击 V2EX Polish 的扩展图标，查看已添加的主题。

**支持预加载多页回复，让嵌套回复更完美**

以前，V2EX Polish 无法处理超过一页的回复的嵌套关系，但是现在，在开启「预加载多页回复」后，可以完整地查看多页回复组成的“楼中楼”。

**完善用户配置的备份与同步**

你可以将 V2EX Polish 配置都保存进 [V2EX 记事本](https://www.v2ex.com/notes) 中，这些配置包括你设置的用户标签、稍后阅读列表、个性化选项，在跨浏览器、跨设备使用 V2EX Polish 时，都能使用同一份配置。

**回复时自动添加楼层号**

当你回复的人在页面内的回复超过一条时，会自动添加楼层号，这会增加楼中楼识别的准确率，让楼层嵌套更合理。

**支持主题颜色自动跟随系统选择**

在开启“自动跟随系统切换浅色/深色模式”后，你可以根据当前系统的选择自动切换 V2EX 的浅色和深色模式。
