/* 解决 tailwindCSS 和 radixCSS 顺序冲突的问题： https://github.com/radix-ui/themes/issues/109 */
@import url('tailwindcss/base');
@import url('@radix-ui/themes/styles.css');

@tailwind components;
@tailwind utilities;

:root {
  --v2p-color-main-50: #f7f9fb;
  --v2p-color-main-100: #f1f5f9;
  --v2p-color-main-200: #e2e8f0;
  --v2p-color-main-300: #cbd5e1;
  --v2p-color-main-350: #94a3b8cc;
  --v2p-color-main-400: #94a3b8;
  --v2p-color-main-500: #64748b;
  --v2p-color-main-600: #475569;
  --v2p-color-main-700: #334155;
  --v2p-color-main-800: #1e293b;
  --v2p-color-accent-50: #ecfdf5;
  --v2p-color-accent-100: #d1fae5;
  --v2p-color-accent-200: #a7f3d0;
  --v2p-color-accent-300: #6ee7b7;
  --v2p-color-accent-400: #34d399;
  --v2p-color-accent-500: #10b981;
  --v2p-color-accent-600: #059669;
  --v2p-color-content: #fff;
  --v2p-box-shadow: 0 3px 5px 0 rgb(0 0 0 / 4%);
  --v2p-color-border: var(--v2p-color-main-200);
  --v2p-color-foreground: var(--v2p-color-main-800);
  --v2p-color-background: #f2f3f5;
  --v2p-color-bg-input: var(--v2p-color-main-50);
  --v2p-color-bg-subtle: rgb(236 253 245 / 90%);

  .radix-themes {
    --cursor-button: pointer;
  }
}

.theme-dark {
  --v2p-color-main-50: #1c2127;
  --v2p-color-main-100: #2d333b;
  --v2p-color-main-200: #374151;
  --v2p-color-main-300: #374151;
  --v2p-color-main-350: #6b7280cc;
  --v2p-color-main-400: #6b7280;
  --v2p-color-main-500: #9ca3af;
  --v2p-color-main-600: #9ca3af;
  --v2p-color-main-700: #d1d5db;
  --v2p-color-main-800: #e5e7eb;
  --v2p-color-accent-50: #064e3b;
  --v2p-color-accent-100: #065f46;
  --v2p-color-accent-200: #047857;
  --v2p-color-accent-300: #059669;
  --v2p-color-accent-400: #10b981;
  --v2p-color-accent-500: #34d399;
  --v2p-color-accent-600: #6ee7b7;
  --v2p-color-content: #22272e;
  --v2p-color-border: #444c56;
  --v2p-color-foreground: #adbac7;
  --v2p-color-background: #1c2128;
  --v2p-color-bg-input: var(--v2p-color-background);
  --v2p-color-bg-subtle: rgb(6 78 59 / 30%);
}

::selection {
  color: currentcolor;
  background-color: rgb(30 41 59 / 10%);
}

.text-with-shadow {
  text-shadow: 2px 2px 0 var(--v2p-color-main-300);
}

@media (width >= 768px) {
  .text-with-shadow {
    text-shadow: 2px 4px 0 var(--v2p-color-main-300);
  }
}

.text-polish {
  text-shadow: 2px 4px 0 var(--v2p-color-main-600);
  -webkit-text-fill-color: #fff;
  -webkit-text-stroke-color: var(--v2p-color-main-800);
  -webkit-text-stroke-width: 2px;
}
