{"name": "v2ex-polish-website", "private": true, "description": "Official homepage of the V2EX Polish extension.", "license": "UNLECENSED", "scripts": {"build": "next build", "deps": "pnpm up --interactive --latest", "dev": "next dev", "lint": "run-p lint:ts lint:es lint:css", "lint:css": "stylelint **/*.css", "lint:es": "eslint **/*.{ts,tsx}", "lint:ts": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "start": "next start"}, "dependencies": {"@radix-ui/themes": "^2.0.3", "@splitbee/web": "^0.3.0", "cheerio": "1.0.0-rc.12", "contentlayer": "^0.3.4", "date-fns": "^3.6.0", "eslint-plugin-jsx-a11y": "^6.8.0", "html-to-image": "^1.11.11", "lucide-react": "^0.378.0", "next": "^14.2.3", "next-contentlayer": "^0.3.4", "react": "18.3.1", "react-dom": "18.3.1", "react-use-event-hook": "^0.9.6", "valibot": "^0.30.0"}, "devDependencies": {"@codennnn/tsconfig": "^1.2.1", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/typography": "^0.5.13", "@types/mdx": "^2.0.13", "@types/react": "18.3.2", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "npm-run-all": "^4.1.5", "postcss": "^8.4.38", "postcss-import": "^16.1.0", "prefer-code-style": "2.1.1", "stylelint": "^16.5.0", "tailwindcss": "^3.4.3", "typescript": "5.4.5"}, "engines": {"node": ">=18", "pnpm": "^8 || ^9"}}