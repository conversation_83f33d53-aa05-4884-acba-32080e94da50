<!DOCTYPE html>

<html>
  <head lang="zh-CN">
    <title>V2EX Polish - 浏览器插件</title>
    <meta
      name="description"
      content="专为 V2EX 用户设计的浏览器插件，丰富的扩展功能为你带来出色的体验。"
    />

    <meta content="website" property="og:type" />
    <meta content="V2EX Polish - 浏览器插件" property="og:title" />
    <meta
      content="专为 V2EX 用户设计的浏览器插件，丰富的扩展功能为你带来出色的体验。"
      property="og:description"
    />
    <meta content="https://v2p.vercel.app" property="og:url" />
    <meta content="https://i.imgur.com/q2minty.png" property="og:image" />

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="google-site-verification" content="yqNxESwZSyvaUchTJ15dVFjmmMbORaHXLDlNTJfX9T4" />

    <link
      href="/favicon.svg"
      rel="icon"
      type="image/svg+xml"
      media="(prefers-color-scheme: light)"
    />
    <link
      href="/favicon-dark.svg"
      rel="icon"
      type="image/svg+xml"
      media="(prefers-color-scheme: dark)"
    />

    <style>
      @import url('https://fonts.googleapis.com/css2?family=Noto+Sans:wght@900&display=swap');

      ::selection {
        color: currentColor;
        background-color: rgb(30 41 59 / 10%);
      }

      *,
      *::before,
      *::after {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        border-color: currentColor;
        border-style: solid;
        border-width: 0;
      }

      :root {
        --color-main: #1e293b;
        --color-fade: #cbd5e1;
        --color-background: #fff;

        background-color: var(--color-background);
      }

      body {
        width: 100vw;
        overflow-x: hidden;
        overflow-y: auto;
        color: var(--color-main);
        font-family: system-ui, sans-serif;
        background-image: linear-gradient(to bottom, rgb(30 41 59 / 10%), transparent);
      }

      main {
        min-height: 100vh;
        padding: 50px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .hero {
        display: flex;
        flex-direction: column;
        align-items: center;
        box-sizing: border-box;
      }

      .hero-logo {
        width: 70px;
        height: 70px;
      }

      .hero-title {
        display: flex;
        gap: 0 30px;
        align-items: center;
        justify-content: center;
        font-weight: bolder;
      }

      .hero-title h1 {
        margin: 0;
        font-size: 75px;
        font-family: 'Noto Sans';
        text-shadow: 0 4px 0 var(--color-fade);
      }

      .text-border {
        -webkit-text-fill-color: var(--color-background);
        -webkit-text-stroke-width: 2px;
        -webkit-text-stroke-color: var(--color-main);
        text-shadow: 0 4px 0 var(--color-main);
      }

      .hero-sub-heading {
        margin: 30px 0 60px;
        font-size: 20px;
        line-height: 1.6;
        text-align: center;
      }

      .actions {
        display: flex;
        gap: 10px 25px;
        align-items: center;
        justify-content: center;
        user-select: none;
      }

      .action {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 15px 35px;
        overflow: hidden;
        color: inherit;
        font-weight: bold;
        text-decoration: none;
        border: 2px solid currentColor;
        border-radius: 8px;
        box-shadow: 0 5px 0 -2px var(--color-fade);
        cursor: pointer;
      }

      @media (hover: hover) {
        .action:hover {
          background-color: rgb(229 229 229);
        }

        .action:hover .icon-chrome {
          width: 0;
          margin-right: 0;
          opacity: 0;
        }

        .action:hover .icon-arrow {
          width: 24px;
          margin-left: 5px;
          opacity: 1;
        }
      }

      .icon-chrome {
        margin-right: 5px;
        transition: all 0.3s;
      }

      .icon-arrow {
        width: 0;
        margin-left: 0;
        transform: rotateZ(-0.25turn);
        opacity: 0;
        transition: all 0.3s;
      }

      .features {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 60px 50px;
        width: 1000px;
        margin: 150px auto 0;
      }

      .features > div > h2 {
        margin-bottom: 20px;
      }

      .features > div > p {
        font-size: 15px;
      }

      .features .last ~ div > p {
        text-indent: -1.25em;
        padding-right: 5px;
      }

      @media screen and (max-width: 1000px) {
        main {
          display: block;
          padding: 10vh 20px;
        }

        .hero-sub-heading {
          font-size: 18px;
        }

        .hero-title h1 {
          line-height: 1;
          text-align: center;
        }

        .hero-logo {
          display: none;
        }

        .actions {
          flex-direction: column;
        }

        .action {
          width: 220px;
        }

        .features {
          grid-template-columns: 2fr;
          justify-content: center;
          width: 100%;
          margin: 100px auto 0;
        }

        .features h2 {
          font-size: 20px;
          text-align: center;
        }

        .features p {
          font-size: 14px;
          text-align: center;
        }

        .features .last ~ div > p {
          text-indent: 0;
          padding-right: 0;
        }
      }
    </style>
  </head>

  <body>
    <main>
      <section>
        <div class="hero">
          <div class="hero-title">
            <div class="hero-logo">
              <svg fill="none" version="1.1" viewBox="0 0 80 80">
                <g style="mix-blend-mode: passthrough">
                  <g style="mix-blend-mode: passthrough">
                    <path
                      d="M79.92689732971192,78.2712C79.92689732971192,78.2712,79.92689732971192,78.2712,79.92689732971192,78.2712C79.92689732971192,78.2712,79.92689732971192,78.2712,79.92689732971192,78.2712C79.92689732971192,78.2712,79.92689732971192,78.2233,79.92689732971192,78.2233C79.92689732971192,78.2233,79.92689732971192,78.2233,79.92689732971192,78.2233C79.9281973297119,78.202,79.9281973297119,78.1805,79.92689732971192,78.1592C79.92689732971192,78.1592,40.88879732971191,0.654873,40.88879732971191,0.654873C40.88879732971191,0.654873,40.88879732971191,0.654873,40.88879732971191,0.654873C40.88879732971191,0.654873,40.83569732971191,0.558854,40.83569732971191,0.558854C40.68919732971192,0.285732,40.45849732971192,0.0942472,40.19479732971192,0.0266865C39.930997329711914,-0.0408741,39.655797329711916,0.0210478,39.42989732971191,0.198777C39.310697329711914,0.288453,39.20699732971191,0.405326,39.124897329711914,0.542849C39.124897329711914,0.542849,39.07179732971191,0.646871,39.07179732971191,0.646871C36.45469732971191,5.86398,33.81986732971191,11.0677,31.167357329711912,16.2582C30.631117329711913,17.189,30.631117329711913,18.424,31.167357329711912,19.3548C34.673097329711915,26.2362,38.16779732971192,33.1203,41.65129732971191,40.007C42.23399732971191,41.0143,42.23399732971191,42.3528,41.65129732971191,43.3598C35.90869732971191,54.7542,29.841107329711914,66.7328,24.045393329711914,78.1751C23.780574329711914,78.7765,23.967055029711915,79.5231,24.463162329711913,79.8476C24.609905329711914,79.9428,24.774012329711915,79.9922,24.940617329711912,79.9915C24.940617329711912,79.9915,41.777297329711914,79.9915,41.777297329711914,79.9915C42.15439732971191,79.9945,42.50169732971192,79.7449,42.679297329711915,79.3433C42.679297329711915,79.3433,42.679297329711915,79.3433,42.679297329711915,79.3433C42.679297329711915,79.3433,43.25609732971191,78.1751,43.25609732971191,78.1751C43.25609732971191,78.1751,51.06109732971191,62.6999,51.06109732971191,62.6999C51.06109732971191,62.6999,51.06109732971191,62.6999,51.06109732971191,62.6999C51.19009732971192,62.4118,51.40909732971191,62.1982,51.66959732971191,62.1064C51.92999732971191,62.0147,52.210097329711914,62.0528,52.44709732971191,62.2118C52.58909732971192,62.3021,52.70999732971191,62.4342,52.79839732971192,62.596C52.79839732971192,62.596,52.79839732971192,62.596,52.79839732971192,62.596C52.79839732971192,62.596,52.79839732971192,62.596,52.79839732971192,62.596C52.79839732971192,62.596,60.53049732971191,77.7991,60.53049732971191,77.7991C60.53049732971191,77.7991,61.24679732971192,79.2154,61.24679732971192,79.2154C61.42699732971192,79.6087,61.76949732971191,79.8538,62.14199732971191,79.8555C62.14199732971191,79.8555,62.61289732971191,79.8555,62.61289732971191,79.8555C62.69229732971191,79.8635,62.771997329711915,79.8635,62.85149732971191,79.8555C62.85149732971191,79.8555,78.97879732971191,79.8555,78.97879732971191,79.8555C79.54009732971191,79.8555,79.99629732971192,79.3086,79.99999732971192,78.6312C79.99059732971192,78.5076,79.96589732971191,78.3862,79.92689732971192,78.2712C79.92689732971192,78.2712,79.92689732971192,78.2712,79.92689732971192,78.2712Z"
                      fill="#1E293B"
                      fill-opacity="1"
                    />
                    <path d="" fill="#1E293B" fill-opacity="1" />
                  </g>
                  <g style="mix-blend-mode: passthrough">
                    <path
                      d="M35.0467,44.128296810913085C35.0467,44.128296810913085,35.6104,43.007996810913085,35.6104,43.007996810913085C36.0867,42.18339681091308,36.0867,41.088096810913086,35.6104,40.263396810913086C35.6104,40.263396810913086,32.1489,33.49401681091309,32.1489,33.49401681091309C32.1489,33.49401681091309,28.1702,25.756388810913087,28.1702,25.756388810913087C28.029,25.476663810913085,27.8009,25.276877810913085,27.5367,25.201693010913086C27.2723,25.126508010913085,26.9942,25.182195010913087,26.7644,25.356304810913088C26.6113,25.466465810913085,26.4854,25.623916810913087,26.3997,25.812400810913086C26.3997,25.812400810913086,0.119977,78.18349681091308,0.119977,78.18349681091308C-0.144272,78.7822968109131,0.0424864,79.5262968109131,0.537745,79.84789681091308C0.684797,79.95029681091309,0.852059,80.00269681091308,1.02183,79.99989681091309C1.02183,79.99989681091309,16.4594,79.99989681091309,16.4594,79.99989681091309C16.8323,79.9996968109131,17.1754,79.75419681091309,17.3546,79.35959681091309C17.3546,79.35959681091309,17.3546,79.35959681091309,17.3546,79.35959681091309C17.3546,79.35959681091309,17.9448,78.17539681091309,17.9448,78.17539681091309C17.9714,78.13199681091308,17.9958,78.08639681091309,18.0177,78.03939681091308C18.0177,78.03939681091308,18.0177,78.03939681091308,18.0177,78.03939681091308C18.0177,78.03939681091308,18.6808,76.7591968109131,18.6808,76.7591968109131C24.1781,65.75669681091308,29.4434,55.250696810913084,34.9539,44.30429681091309C34.988,44.24809681091308,35.019,44.189296810913085,35.0467,44.128296810913085C35.0467,44.128296810913085,35.0467,44.128296810913085,35.0467,44.128296810913085Z"
                      fill="#1E293B"
                      fill-opacity="1"
                    />
                    <path d="" fill="#1E293B" fill-opacity="1" />
                  </g>
                </g>
              </svg>
            </div>
            <h1>
              V2EX
              <span class="text-border">Polish</span>
            </h1>
          </div>

          <p class="hero-sub-heading">
            专为 V2EX 用户设计的浏览器插件，丰富的扩展功能为你带来出色的体验。
          </p>

          <div class="actions">
            <a class="action" href="https://github.com/coolpace/V2EX_Polish" target="_blank">
              GitHub 开源
            </a>
            <a
              class="action"
              href="https://chrome.google.com/webstore/detail/v2ex-polish/onnepejgdiojhiflfoemillegpgpabdm?hl=zh-CN&authuser=0"
              target="_blank"
            >
              <svg
                class="icon-chrome"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <circle cx="12" cy="12" r="4"></circle>
                <line x1="21.17" y1="8" x2="12" y2="8"></line>
                <line x1="3.95" y1="6.06" x2="8.54" y2="14"></line>
                <line x1="10.88" y1="21.94" x2="15.46" y2="14"></line>
              </svg>
              安装至 Chrome
              <svg class="icon-arrow" width="24" height="24" viewBox="0 0 24 24">
                <path
                  fill="#1D354F"
                  d="M6.293 8.793a1 1 0 0 1 1.32-.083l.094.083L12 13.085l4.293-4.292a1 1 0 0 1 1.32-.083l.094.083a1 1 0 0 1 .083 1.32l-.083.094-5 5a1 1 0 0 1-1.32.083l-.094-.083-5-5a1 1 0 0 1 0-1.414z"
                ></path>
              </svg>
            </a>
          </div>
        </div>
      </section>

      <section>
        <div class="features">
          <div>
            <h2>🪄 界面美化</h2>
            <p>UI 设计更现代化，为你带来愉悦的视觉体验。</p>
          </div>
          <div>
            <h2>📥 评论回复嵌套层级</h2>
            <p>主题下的评论回复支持层级展示，可以更轻松地跟踪和回复其他用户的评论。</p>
          </div>
          <div>
            <h2>🔥 热门回复展示</h2>
            <p>自动筛选出最受欢迎的回复，第一时间追上热评。</p>
          </div>
          <div>
            <h2>😀 表情回复支持</h2>
            <p>评论输入框可以选择表情，让回复更加生动和有趣。</p>
          </div>
          <div>
            <h2>📃 长回复优化</h2>
            <p>智能折叠长篇回复，一键展开查看完整内容。</p>
          </div>
          <div class="last">
            <h2>📰 内置主题列表</h2>
            <p>无需打开网页，插件内即可快速获取最热、最新的主题列表和消息通知。</p>
          </div>
          <div>
            <p>⊙ 便捷回复操作：文字转 Base64、上传图片。</p>
          </div>
          <div>
            <p>⊙ 添加用户信息卡片，快捷查看用户信息。</p>
          </div>
          <div>
            <p>⊙ 右键菜单扩展：支持解析页面中所有 Base64 编码的内容。</p>
          </div>
          <div>
            <p>⊙ 在主题列表中即可预览内容，无需再进入主题页面。</p>
          </div>
          <div>
            <p>⊙ 翻页后自动跳转到回复区。</p>
          </div>
          <div>
            <p>⊙ 自动领取每日签到奖励。</p>
          </div>
          <div>
            <p>⊙ 用户标签设置，快速标记各类用户。</p>
          </div>
          <div>
            <p>⊙ 支持备份个人配置，方便跨设备、跨脚本同步配。</p>
          </div>
          <div>
            <p>⊙ 支持自动跟随系统切换浅色/深色主题。</p>
          </div>
          <div>
            <p>⊙ 支持 SOV2EX 作为搜索选项。</p>
          </div>
          <div>
            <p>⊙ 支持预加载多页回复，让嵌套回复更完美。</p>
          </div>
          <div>
            <p>⊙ “稍后阅读”功能：添加感兴趣的主题，方便日后浏览。</p>
          </div>
          <div>
            <p>⊙ ...... 更多功能等你一探究竟！</p>
          </div>
        </div>
      </section>
    </main>
  </body>
</html>
