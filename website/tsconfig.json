{"extends": "@codennnn/tsconfig/next.json", "compilerOptions": {"baseUrl": "./", "paths": {"~/*": ["./src/*"], "contentlayer/generated": ["./.contentlayer/generated"]}, "plugins": [{"name": "next"}], "strictNullChecks": true}, "include": ["next-env.d.ts", "src/**/*", ".next/types/**/*.ts", "./tailwind.config.ts", "./contentlayer.config.ts", ".contentlayer/generated"], "exclude": ["node_modules", "**/.*/", ".git"]}