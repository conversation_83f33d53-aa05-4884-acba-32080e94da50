<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V2EX Polish 最终导航栏测试</title>
    <link rel="stylesheet" href="extension/css/v2ex-theme-var.css">
    <link rel="stylesheet" href="extension/css/v2ex-theme-dark.css">
    <link rel="stylesheet" href="extension/css/v2ex-layout-improvements.css">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: system-ui, sans-serif;
            transition: all 0.3s ease;
            min-height: 100vh;
        }
        
        /* 模拟V2EX的顶部导航栏 */
        #Top {
            height: var(--v2p-nav-height);
            background-color: var(--v2p-color-bg-content);
            border-bottom: 1px solid var(--v2p-color-border);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        #Top .content {
            max-width: 1200px;
            margin: 0 auto;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .site-nav {
            height: 100%;
            display: flex;
            align-items: center;
        }
        
        .tools {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .logo {
            font-size: 20px;
            font-weight: bold;
            color: var(--v2p-color-foreground);
            text-decoration: none;
        }
        
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            background: var(--v2p-color-background);
            min-height: calc(100vh - var(--v2p-nav-height));
        }
        
        .demo-section {
            background: var(--v2p-color-bg-content);
            border: 1px solid var(--v2p-color-border);
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .demo-section h2 {
            color: var(--v2p-color-foreground);
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .demo-section h3 {
            color: var(--v2p-color-foreground);
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .demo-section p {
            color: var(--v2p-color-font-secondary);
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-item {
            background: var(--v2p-color-bg-reply);
            border: 1px solid var(--v2p-color-border);
            border-radius: 8px;
            padding: 20px;
        }
        
        .feature-item h4 {
            color: var(--v2p-color-foreground);
            margin: 0 0 10px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .feature-item .icon {
            width: 20px;
            height: 20px;
            color: #10b981;
        }
        
        .nav-buttons-demo {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 20px 0;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-success {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        
        .status-info {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        
        .theme-toggle-demo {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 12px 24px;
            background: var(--v2p-color-button-background);
            color: var(--v2p-color-button-foreground);
            border: 1px solid var(--v2p-color-border);
            border-radius: 8px;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.2s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .theme-toggle-demo:hover {
            background: var(--v2p-color-button-background-hover);
            transform: translateY(-2px);
        }
        
        .theme-toggle-demo i {
            width: 18px;
            height: 18px;
        }
    </style>
</head>
<body class="v2p-theme-dark-default" id="Wrapper">
    <!-- 模拟V2EX顶部导航栏 -->
    <div id="Top">
        <div class="content">
            <a href="/" class="logo">V2EX</a>
            <nav class="site-nav">
                <div class="tools">
                    <a href="/" class="v2p-nav-btn v2p-nav-icon-only" title="首页">
                        <span class="v2p-nav-icon">
                            <i data-lucide="Home"></i>
                        </span>
                    </a>
                    
                    <a href="/notes" class="v2p-nav-btn v2p-nav-icon-only" title="记事本">
                        <span class="v2p-nav-icon">
                            <i data-lucide="notebook-pen"></i>
                        </span>
                    </a>
                    
                    <a href="/timeline" class="v2p-nav-btn v2p-nav-icon-only" title="时间轴">
                        <span class="v2p-nav-icon">
                            <i data-lucide="Clock"></i>
                        </span>
                    </a>
                    
                    <a href="/settings" class="v2p-nav-btn v2p-nav-icon-only" title="设置">
                        <span class="v2p-nav-icon">
                            <i data-lucide="Settings"></i>
                        </span>
                    </a>
                    
                    <a href="/solana/tips" class="v2p-nav-btn v2p-nav-icon-only" title="SOL打赏记录">
                        <span class="v2p-nav-icon">
                            <i data-lucide="Coins"></i>
                        </span>
                    </a>
                    
                    <a href="/solana/hodl" class="v2p-nav-btn v2p-nav-icon-only" title="HODL">
                        <span class="v2p-nav-icon">
                            <i data-lucide="TrendingUp"></i>
                        </span>
                    </a>
                    
                    <a href="/notifications" class="v2p-nav-btn v2p-nav-icon-only" title="未读提醒">
                        <span class="v2p-nav-icon">
                            <i data-lucide="Bell"></i>
                        </span>
                        <span class="v2p-unread-badge">7</span>
                    </a>
                    
                    <a href="/write" class="v2p-nav-btn v2p-nav-icon-only" title="创作新主题">
                        <span class="v2p-nav-icon">
                            <i data-lucide="Plus"></i>
                        </span>
                    </a>
                    
                    <button class="v2p-nav-btn v2p-nav-icon-only v2p-theme-toggle" title="切换主题">
                        <span class="v2p-nav-icon">
                            <i data-lucide="Sun"></i>
                        </span>
                    </button>
                    
                    <a href="/member/testuser" class="v2p-user-avatar" title="测试用户">
                        <img src="https://cdn.v2ex.com/gravatar/test?s=64&d=identicon" alt="测试用户" />
                    </a>
                </div>
            </nav>
        </div>
    </div>

    <div class="main-content">
        <div class="demo-section">
            <h2>✅ 所有问题已修复</h2>
            <p>这个页面展示了V2EX Polish导航栏的最终版本，所有问题都已得到解决。</p>
            
            <div class="feature-grid">
                <div class="feature-item">
                    <h4>
                        <i data-lucide="CheckCircle" class="icon"></i>
                        图标显示修复
                    </h4>
                    <p>记事本图标现在正确显示，移除了多余的NotebookPen引用。</p>
                    <div class="status-indicator status-success">
                        <i data-lucide="notebook-pen"></i>
                        记事本图标正常
                    </div>
                </div>
                
                <div class="feature-item">
                    <h4>
                        <i data-lucide="CheckCircle" class="icon"></i>
                        圆形按钮设计
                    </h4>
                    <p>所有导航按钮现在都是圆形，不仅仅是悬停时才变圆形。</p>
                    <div class="status-indicator status-success">
                        <i data-lucide="Circle"></i>
                        一直保持圆形
                    </div>
                </div>
                
                <div class="feature-item">
                    <h4>
                        <i data-lucide="CheckCircle" class="icon"></i>
                        正确的链接地址
                    </h4>
                    <p>SOL打赏记录指向正确地址：/solana/tips</p>
                    <div class="status-indicator status-success">
                        <i data-lucide="Coins"></i>
                        /solana/tips
                    </div>
                </div>
                
                <div class="feature-item">
                    <h4>
                        <i data-lucide="CheckCircle" class="icon"></i>
                        新增HODL功能
                    </h4>
                    <p>添加了新的HODL导航项，指向：/solana/hodl</p>
                    <div class="status-indicator status-info">
                        <i data-lucide="TrendingUp"></i>
                        /solana/hodl
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🎯 完整导航功能</h3>
            <p>以下是所有导航按钮的完整列表：</p>
            
            <div class="nav-buttons-demo">
                <a href="/" class="v2p-nav-btn v2p-nav-icon-only" title="首页">
                    <span class="v2p-nav-icon"><i data-lucide="Home"></i></span>
                </a>
                <a href="/notes" class="v2p-nav-btn v2p-nav-icon-only" title="记事本">
                    <span class="v2p-nav-icon"><i data-lucide="notebook-pen"></i></span>
                </a>
                <a href="/timeline" class="v2p-nav-btn v2p-nav-icon-only" title="时间轴">
                    <span class="v2p-nav-icon"><i data-lucide="Clock"></i></span>
                </a>
                <a href="/settings" class="v2p-nav-btn v2p-nav-icon-only" title="设置">
                    <span class="v2p-nav-icon"><i data-lucide="Settings"></i></span>
                </a>
                <a href="/solana/tips" class="v2p-nav-btn v2p-nav-icon-only" title="SOL打赏记录">
                    <span class="v2p-nav-icon"><i data-lucide="Coins"></i></span>
                </a>
                <a href="/solana/hodl" class="v2p-nav-btn v2p-nav-icon-only" title="HODL">
                    <span class="v2p-nav-icon"><i data-lucide="TrendingUp"></i></span>
                </a>
                <a href="/notifications" class="v2p-nav-btn v2p-nav-icon-only" title="未读提醒">
                    <span class="v2p-nav-icon"><i data-lucide="Bell"></i></span>
                    <span class="v2p-unread-badge">5</span>
                </a>
                <a href="/write" class="v2p-nav-btn v2p-nav-icon-only" title="创作新主题">
                    <span class="v2p-nav-icon"><i data-lucide="Plus"></i></span>
                </a>
                <button class="v2p-nav-btn v2p-nav-icon-only v2p-theme-toggle" title="主题切换">
                    <span class="v2p-nav-icon"><i data-lucide="Sun"></i></span>
                </button>
            </div>
        </div>

        <div class="demo-section">
            <h3>🌙 暗夜模式特效</h3>
            <p>在暗夜模式下，导航按钮具有特殊的视觉效果：</p>
            <ul>
                <li><strong>默认状态：</strong>纯黑色背景，白色图标，圆形形状</li>
                <li><strong>悬停状态：</strong>白色背景，黑色图标，保持圆形</li>
                <li><strong>动画效果：</strong>平滑的颜色过渡和轻微缩放</li>
            </ul>
        </div>
    </div>

    <button class="theme-toggle-demo" onclick="toggleTheme()">
        <i data-lucide="Palette"></i>
        <span>切换主题模式</span>
    </button>

    <script>
        // 初始化图标
        lucide.createIcons();
        
        // 主题切换功能
        function toggleTheme() {
            const body = document.body;
            const wrapper = document.getElementById('Wrapper');
            const isNight = body.classList.contains('v2p-theme-dark-default');
            
            if (isNight) {
                body.classList.remove('v2p-theme-dark-default');
                body.classList.add('v2p-theme-light-default');
                wrapper.classList.remove('Night');
            } else {
                body.classList.remove('v2p-theme-light-default');
                body.classList.add('v2p-theme-dark-default');
                wrapper.classList.add('Night');
            }
            
            // 更新主题切换按钮图标
            const themeToggles = document.querySelectorAll('.v2p-theme-toggle i');
            themeToggles.forEach(toggle => {
                toggle.setAttribute('data-lucide', isNight ? 'Moon' : 'Sun');
            });
            lucide.createIcons();
        }
        
        // 为导航栏的主题切换按钮添加事件
        document.querySelector('.v2p-theme-toggle').addEventListener('click', toggleTheme);
        
        // 模拟未读消息数量变化
        let unreadCount = 7;
        setInterval(() => {
            unreadCount = Math.floor(Math.random() * 10);
            const badges = document.querySelectorAll('.v2p-unread-badge');
            badges.forEach(badge => {
                if (unreadCount > 0) {
                    badge.textContent = unreadCount > 9 ? '9+' : unreadCount.toString();
                    badge.style.display = 'inline-flex';
                } else {
                    badge.style.display = 'none';
                }
            });
        }, 4000);
    </script>
</body>
</html>
