<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <title>V2EX Polish 用户面板</title>

    <link rel="stylesheet" href="../css/reset.css" />
    <link rel="stylesheet" href="../css/v2ex-theme-var.css" />
    <link rel="stylesheet" href="../css/v2ex-theme-dark.css" />
    <link rel="stylesheet" href="../css/v2ex-theme-dawn.css" />
    <link rel="stylesheet" href="../css/popup.css" />
  </head>

  <body>
    <main>
      <nav>
        <ul class="tabs">
          <li data-target="tab-reading">稍后阅读</li>
          <li data-target="tab-hot">最热</li>
          <li data-target="tab-latest">最新</li>
          <li data-target="tab-message">消息</li>

          <li class="tabs-right">
            <ul class="tabs">
              <li data-target="tab-feature" title="更多功能">
                <span style="width: 18px; height: 18px">
                  <i data-lucide="shapes"></i>
                </span>
              </li>
              <li data-target="tab-setting" title="设置">
                <span style="width: 18px; height: 18px">
                  <i data-lucide="settings"></i>
                </span>
              </li>
            </ul>
          </li>
        </ul>
      </nav>

      <section>
        <div class="tab-contents">
          <div id="tab-reading" class="tab-content"></div>
          <div id="tab-hot" class="tab-content"></div>
          <div id="tab-latest" class="tab-content"></div>
          <div id="tab-message" class="tab-content"></div>

          <div id="tab-feature" class="tab-content">
            <div class="features">
              <div class="feature feature-check-in">
                <div class="feature-title">每日签到</div>
                <div class="feature-content"></div>
              </div>
            </div>
          </div>

          <div id="tab-setting" class="tab-content">
            <div class="settings">
              <div class="settings-top">
                <button id="open-options" class="action-btn">
                  <span class="action-icon">
                    <i data-lucide="settings"></i>
                  </span>
                  更多设置选项
                </button>
              </div>

              <hr />

              <form id="settings-form">
                <div>
                  <h2>
                    <label for="pat">设置您的个人访问令牌（PAT）</label>
                  </h2>

                  <div class="pat-input">
                    <input id="pat" type="text" placeholder="填写你的个人访问令牌 (PAT)" />
                    <button class="submit-btn" type="submit">保存令牌</button>
                  </div>

                  <div class="description">
                    <div class="description-title">这将授权 V2EX Polish 使用令牌：</div>
                    <ul>
                      <li>
                        <span class="description-icon">
                          <i data-lucide="check"></i>
                        </span>
                        <p>
                          请求
                          <a href="https://www.v2ex.com/help/api" target="_blank">
                            V2EX 的开放 API
                          </a>
                          以获取所需数据。
                        </p>
                      </li>
                      <li>
                        <span class="description-icon">
                          <i data-lucide="check"></i>
                        </span>
                        <p>自动更新令牌（需手动开启）。</p>
                      </li>
                    </ul>

                    <div class="learn-more">
                      <a
                        class="link"
                        href="https://www.v2ex.com/help/personal-access-token"
                        target="_blank"
                      >
                        了解更多
                      </a>
                    </div>

                    <details class="details">
                      <summary>
                        <span class="summary-text">如何获取 PAT？哪些功能需要它？</span>
                      </summary>
                      <div class="details-content">
                        <p>
                          如果您还没有 PAT，请
                          <a
                            class="link"
                            href="https://www.v2ex.com/settings/tokens"
                            target="_blank"
                          >
                            前往创建
                          </a>
                          。
                        </p>
                        <p>
                          主题内容预览、消息通知等功能都需要使用 PAT
                          请求接口数据，然后作相应的展示。请放心，我们绝不会以任何方式滥用您的 PAT。
                        </p>
                      </div>
                    </details>
                  </div>

                  <hr />

                  <div>
                    <h2>API 使用情况</h2>
                    <fieldset>
                      <div class="form-item">
                        <label>每小时限制请求</label>
                        <input id="limit" readonly />
                      </div>

                      <div class="form-item">
                        <label>剩余请求次数</label>
                        <input id="remaining" readonly />
                      </div>

                      <div class="form-item">
                        <label>下次重置时间</label>
                        <input id="reset" readonly />
                      </div>
                    </fieldset>
                  </div>
                </div>
              </form>

              <div>
                <span class="storage-data-bar">
                  <span>已缓存数据：<span class="storage-size">-</span></span>
                  <button id="clear-storage" type="button">清空</button>
                </span>
              </div>

              <hr />

              <div>
                <h2>配置备份与同步<sup>实验性</sup></h2>

                <p style="color: var(--v2p-color-font-tertiary); margin-bottom: 10px">
                  （配置备份是实验性的功能，后续版本可能会调整，请勿过于依赖。）
                </p>

                <div>
                  将所有配置都保存进
                  <a href="https://www.v2ex.com/notes">V2EX 记事本 </a
                  >中，方便跨浏览器或跨脚本同步配置。
                </div>

                <fieldset style="margin-top: 8px">
                  <div class="form-item">
                    <label>本地使用版本</label>
                    <input id="local-version" value="-" readonly />
                  </div>

                  <div class="form-item">
                    <label>远程备份版本</label>
                    <input id="remote-version" value="-" readonly />
                  </div>

                  <div class="form-item">
                    <label>上次备份时间</label>
                    <input id="last-sync-time" value="-" readonly />
                  </div>

                  <div class="form-item">
                    <label>上次检测更新</label>
                    <input id="last-check-time" value="-" readonly />
                  </div>
                </fieldset>

                <span class="storage-data-bar">
                  <span class="storage-tip">...</span>
                  <button id="sync-settings" type="button" disabled>备份同步</button>
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <template id="reading-empty">
      <div class="empty-block">
        <div class="empty-emoji">¯\_(ツ)_/¯</div>
        <div class="empty-text">暂未发现阅读列表</div>
        <div class="empty-text">添加感兴趣的主题，方便日后阅读</div>
        <div class="empty-img-block">
          <div class="empty-img-block">
            <div class="menu-wrapper">
              <ul class="fake-menu menu-root">
                <li class="fake-item"></li>
                <li class="fake-item"></li>
                <li class="fake-item"></li>
                <li class="item-divider"></li>
                <li class="active-item">
                  <span class="left">
                    <span class="logo">
                      <svg fill="none" viewBox="0 0 88 88">
                        <g style="mix-blend-mode: passthrough">
                          <path
                            d="M87.92 86.098v-.052a.592.592 0 0 0 0-.07L44.978.72l-.059-.105c-.16-.3-.415-.511-.705-.586a.961.961 0 0 0-.841.19 1.315 1.315 0 0 0-.336.378l-.058.115a2571.004 2571.004 0 0 1-8.695 17.172c-.59 1.024-.59 2.382 0 3.406 3.856 7.57 7.7 15.142 11.532 22.718.641 1.108.641 2.58 0 3.688C39.5 60.23 32.826 73.406 26.45 85.993c-.291.661-.086 1.482.46 1.84.16.104.341.158.525.158h18.52c.415.003.797-.272.992-.713l.635-1.285 8.585-17.023c.142-.317.383-.552.67-.653a.949.949 0 0 1 .855.116c.156.1.289.245.386.423l8.506 16.723.787 1.558c.199.433.575.702.985.704h.518c.087.009.175.009.263 0h17.74c.617 0 1.119-.601 1.123-1.347a1.615 1.615 0 0 0-.08-.396Z"
                            fill="currentColor"
                            style="mix-blend-mode: passthrough"
                          />
                          <path
                            d="m38.551 48.541.62-1.232a3.095 3.095 0 0 0 0-3.02l-3.807-7.446-4.377-8.511c-.155-.308-.406-.527-.697-.61a.957.957 0 0 0-.85.17 1.252 1.252 0 0 0-.4.502L.132 86.002c-.29.658-.085 1.477.46 1.83.161.113.345.17.532.168h16.981c.41 0 .788-.27.985-.705l.65-1.302c.029-.048.055-.098.08-.15l.729-1.408c6.047-12.103 11.839-23.66 17.9-35.7.038-.062.072-.127.102-.194Z"
                            fill="currentColor"
                            style="mix-blend-mode: passthrough"
                          />
                        </g>
                      </svg>
                    </span>
                    <span>V2EX Polish</span>
                  </span>
                  <span style="width: 14px; height: 14px">
                    <i data-lucide="chevron-right"></i>
                  </span>
                  <ul class="fake-menu">
                    <li class="fake-item"></li>
                    <li class="active-item select-item">添加进稍后阅读</li>
                  </ul>
                </li>
                <li class="item-divider"></li>
                <li class="fake-item"></li>
                <li class="fake-item"></li>
              </ul>
            </div>
          </div>
          <div class="empty-img-caption">在主题页的空白处右键菜单，选择“添加进稍后阅读”</div>
        </div>
      </div>
    </template>

    <script src="../scripts/jquery.min.js"></script>
    <script src="../scripts/popup.min.js"></script>
  </body>
</html>
