{"name": "v2ex_polish", "version": "0.1.0", "private": "true", "license": "UNLICENSED", "author": "LeoKu (https://github.com/Codennnn)", "scripts": {"dev": "run-p build:manifest watch run:chrome", "dev:firefox": "run-p watch run:firefox", "run:chrome": "web-ext run -t chromium --source-dir ./extension --chromium-profile ./chrome-profile --profile-create-if-missing --keep-profile-changes --start-url https://www.v2ex.com/", "run:firefox": "web-ext run --source-dir ./extension --start-url https://www.v2ex.com/", "build": "bun build:all && bun scripts/build.ts && bun pack:chrome && bun pack:firefox", "build:all": "run-p build:manifest build:style build:ext build:userscript", "build:userscript": "run-s output:css output:userscript", "build:ext": "tsup", "build:manifest": "bun scripts/build-manifest.ts", "build:style": "sass src/styles:extension/css --no-source-map --style=compressed", "pack:chrome": "web-ext build -s build-chrome -a build-chrome -o", "pack:firefox": "web-ext build -s build-firefox -a build-firefox -o", "output:userscript": "tsup src/user-scripts/index.ts --no-minify --outDir dist", "output:css": "bun src/user-scripts/write-style.mjs", "watch": "run-p watch:style watch:ext", "watch:ext": "bun build:ext --watch", "watch:style": "bun build:style --watch", "lint": "run-p lint:ts lint:es lint:style lint:prettier", "lint:ts": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "lint:es": "eslint --config ./.eslintrc.cjs **/*.{js,ts}", "lint:prettier": "prettier --write **/*.{md,json}", "lint:style": "stylelint **/*.{css,scss}", "deps": "pnpm up --interactive --latest"}, "dependencies": {"@floating-ui/dom": "1.4.5", "lucide": "0.445.0", "webext-patterns": "1.5.0"}, "devDependencies": {"@codennnn/tsconfig": "^1.2.1", "@types/chrome": "^0.0.271", "@types/firefox-webext-browser": "^120.0.4", "@types/jquery": "^3.5.30", "@types/node": "^20.14.12", "bun": "^1.1.29", "npm-run-all": "^4.1.5", "postcss-scss": "^4.0.9", "prefer-code-style": "2.1.7", "sass": "^1.79.3", "stylelint": "^16.9.0", "tsup": "^8.3.0", "typescript": "^5.5.4", "web-ext": "^8.3.0"}}