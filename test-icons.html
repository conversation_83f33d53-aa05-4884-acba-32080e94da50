<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标测试</title>
    <style>
        body {
            font-family: system-ui, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .icon-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .icon-item i {
            width: 24px;
            height: 24px;
            color: #333;
        }
        
        .icon-name {
            font-weight: 500;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .error {
            color: #ef4444;
        }
        
        .success {
            color: #10b981;
        }
    </style>
</head>
<body>
    <h1>V2EX Polish 图标测试</h1>
    
    <div class="status" id="status">
        正在加载图标...
    </div>
    
    <div class="icon-grid">
        <div class="icon-item">
            <i data-lucide="Home"></i>
            <span class="icon-name">Home (首页)</span>
        </div>
        
        <div class="icon-item">
            <i data-lucide="notebook-pen"></i>
            <span class="icon-name">notebook-pen (记事本)</span>
        </div>
        
        <div class="icon-item">
            <i data-lucide="Clock"></i>
            <span class="icon-name">Clock (时间轴)</span>
        </div>
        
        <div class="icon-item">
            <i data-lucide="Settings"></i>
            <span class="icon-name">Settings (设置)</span>
        </div>
        
        <div class="icon-item">
            <i data-lucide="Coins"></i>
            <span class="icon-name">Coins (打赏记录)</span>
        </div>
        
        <div class="icon-item">
            <i data-lucide="Bell"></i>
            <span class="icon-name">Bell (未读提醒)</span>
        </div>
        
        <div class="icon-item">
            <i data-lucide="Plus"></i>
            <span class="icon-name">Plus (创作新主题)</span>
        </div>
        
        <div class="icon-item">
            <i data-lucide="Sun"></i>
            <span class="icon-name">Sun (浅色主题)</span>
        </div>
        
        <div class="icon-item">
            <i data-lucide="Moon"></i>
            <span class="icon-name">Moon (深色主题)</span>
        </div>
        
        <!-- 现有的图标 -->
        <div class="icon-item">
            <i data-lucide="MessageSquare"></i>
            <span class="icon-name">MessageSquare (现有)</span>
        </div>
        
        <div class="icon-item">
            <i data-lucide="Heart"></i>
            <span class="icon-name">Heart (现有)</span>
        </div>
        
        <div class="icon-item">
            <i data-lucide="Star"></i>
            <span class="icon-name">Star (现有)</span>
        </div>
    </div>

    <!-- 模拟V2EX Polish的图标加载方式 -->
    <script>
        // 模拟V2EX Polish的loadIcons函数
        function loadIcons() {
            // 检查是否有lucide库
            if (typeof window.lucide === 'undefined') {
                document.getElementById('status').innerHTML = '<span class="error">❌ Lucide库未加载</span>';
                return;
            }
            
            try {
                // 使用与V2EX Polish相同的方式创建图标
                window.lucide.createIcons({
                    attrs: {
                        width: '100%',
                        height: '100%',
                    },
                    icons: {
                        // 新增的图标
                        Home: window.lucide.Home,
                        'notebook-pen': window.lucide.NotebookPen,
                        Clock: window.lucide.Clock,
                        Settings: window.lucide.Settings,
                        Coins: window.lucide.Coins,
                        Bell: window.lucide.Bell,
                        Plus: window.lucide.Plus,
                        Sun: window.lucide.Sun,
                        Moon: window.lucide.Moon,
                        // 现有的图标
                        MessageSquare: window.lucide.MessageSquare,
                        Heart: window.lucide.Heart,
                        Star: window.lucide.Star,
                    },
                });
                
                // 检查图标是否成功渲染
                setTimeout(() => {
                    const iconElements = document.querySelectorAll('[data-lucide]');
                    let successCount = 0;
                    let errorCount = 0;
                    
                    iconElements.forEach(el => {
                        if (el.querySelector('svg')) {
                            successCount++;
                        } else {
                            errorCount++;
                            console.error('图标未渲染:', el.getAttribute('data-lucide'));
                        }
                    });
                    
                    const statusEl = document.getElementById('status');
                    if (errorCount === 0) {
                        statusEl.innerHTML = `<span class="success">✅ 所有图标加载成功 (${successCount}/${iconElements.length})</span>`;
                    } else {
                        statusEl.innerHTML = `<span class="error">⚠️ 部分图标加载失败 (成功: ${successCount}, 失败: ${errorCount})</span>`;
                    }
                }, 100);
                
            } catch (error) {
                document.getElementById('status').innerHTML = `<span class="error">❌ 图标加载错误: ${error.message}</span>`;
                console.error('图标加载错误:', error);
            }
        }
        
        // 动态加载lucide库
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/lucide@latest/dist/umd/lucide.js';
        script.onload = () => {
            console.log('Lucide库加载完成');
            loadIcons();
        };
        script.onerror = () => {
            document.getElementById('status').innerHTML = '<span class="error">❌ 无法加载Lucide库</span>';
        };
        document.head.appendChild(script);
    </script>
</body>
</html>
