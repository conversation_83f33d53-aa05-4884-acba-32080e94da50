@mixin line-clamp($lines: 1, $line-height: 1.4) {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $lines;
  line-height: $line-height;
}

@mixin common-button() {
  cursor: pointer;
  user-select: none;
  position: relative;
  display: inline-flex;
  gap: 5px;
  align-items: center;
  height: 28px;
  padding: 0 12px;
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  line-height: 28px;
  color: var(--v2p-color-button-foreground);
  text-shadow: none;
  white-space: nowrap;
  background: var(--v2p-color-button-background);
  border: none;
  border-radius: 6px;
  outline: none;
  box-shadow:
    0 1.8px 0 var(--box-background-hover-color),
    0 1.8px 0 var(--button-background-color);
  transition:
    color 0.25s,
    background-color 0.25s,
    box-shadow 0.25s;

  &:is(:hover:enabled, :active:enabled) {
    font-weight: 500;
    color: var(--v2p-color-button-foreground-hover);
    text-shadow: none;
    background: var(--v2p-color-button-background-hover);
    border: none;
    box-shadow: var(--button-hover-shadow);
  }

  &:is(.hover_now, .disable_now) {
    color: var(--v2p-color-button-foreground) !important;
    text-shadow: none !important;
    background: var(--button-background-color) !important;
    border: none !important;
    box-shadow:
      0 1.8px 0 var(--box-background-hover-color) !important,
      0 1.8px 0 var(--button-background-color) !important;
  }

  &:is(.disable_now, :disabled) {
    pointer-events: none;
    cursor: default;
    font-weight: 500;
    color: var(--v2p-color-button-foreground);
    text-shadow: none;
    opacity: 0.8;
    background: var(--button-background-color);
    box-shadow:
      0 1.8px 0 var(--box-background-hover-color),
      0 1.8px 0 var(--button-background-color);
  }

  kbd {
    position: relative;
    right: -4px;
    padding: 0 3px;
    font-family: inherit;
    font-size: 90%;
    line-height: initial;
    border: 1px solid var(--button-border-color);
    border-radius: 4px;
  }
}

@mixin hover-button {
  cursor: pointer;
  user-select: none;
  position: relative;
  z-index: 1;
  margin: 0;
  text-decoration: none;
  white-space: nowrap;
  background: none;
  background-color: transparent;
  transition: color 0.2s;

  &::before {
    content: '';
    position: absolute;
    z-index: -1;
    inset: 0 -5px;
    transform: scale(0.65);
    opacity: 0;
    background-color: var(--v2p-color-bg-hover-btn);
    border-radius: 5px;
    transition:
      background-color 0.2s,
      color 0.2s,
      transform 0.2s,
      opacity 0.2s;
  }

  &:hover {
    text-decoration: none;

    &::before {
      transform: scale(1);
      opacity: 1;
    }
  }

  @content;
}

@mixin input($minHeight: unset, $maxHeight: 800px) {
  resize: none;
  overflow: hidden;
  height: unset;
  min-height: $minHeight !important;
  max-height: $maxHeight !important;
  font-size: 15px;
  color: currentColor;
  background-color: var(--v2p-color-bg-input);
  border: 1px solid var(--button-border-color);
  border-radius: 8px;
  transition: opacity 0.25s;

  @content;

  &::placeholder {
    font-size: 15px;
    color: var(--v2p-color-font-tertiary);
  }

  &:is(:focus, :focus-within) {
    background-color: transparent;
    outline: none;
    box-shadow: 0 0 0 1px var(--button-border-color);
  }
}

@mixin tooltip {
  pointer-events: none;
  z-index: var(--zidx-tip);
  overflow: hidden;
  width: max-content;
  min-width: 30px;
  padding: 2px 5px;
  font-size: 12px;
  color: var(--v2p-color-foreground);
  text-align: center;
  white-space: nowrap;
  background-color: var(--v2p-color-bg-tooltip);
  border-radius: 4px;
  box-shadow: var(--v2p-widget-shadow);
}

@mixin link {
  text-decoration: underline 1.5px;
  text-underline-offset: 0.46ex;
}

@mixin popup {
  font-size: 14px;
  background: var(--v2p-color-bg-widget);
  backdrop-filter: blur(16px);
  border: 1px solid var(--box-border-color);
  border-radius: 8px;
  box-shadow: var(--v2p-widget-shadow);
}

@mixin underline-text {
  text-decoration: underline 1px;
  text-underline-offset: var(--v2p-underline-offset);
}

@mixin select-box {
  color: var(--v2p-color-foreground);
  background-color: var(--v2p-color-background);
  border: 1px solid var(--v2p-color-border);
  border-radius: 4px;
}

@mixin text-content {
  font-size: 15px;
  line-height: 1.6;
}
