html,
body {
  height: 100%;
  margin: 0;
}

body {
  font-size: 16px;

  &.v2p-theme-dark-default {
    color-scheme: dark;
  }
}

.layout {
  display: flex;
  height: 100%;
}

.side {
  overflow-y: auto;
  width: 260px;
  height: 100%;
  background-color: var(--v2p-color-background);
  border-right: 1px solid var(--v2p-color-divider);
}

.side-header {
  display: flex;
  gap: 0 14px;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.side-header-title {
  font-size: 26px;
  font-weight: bold;
}

.menu {
  display: flex;
  flex-direction: column;
  gap: 10px 0;
  padding: 20px;
  font-size: 14px;
  font-weight: bold;
}

.menu-item {
  cursor: pointer;
  display: flex;
  gap: 0 10px;
  align-items: center;
  padding: 8px 12px;
  border-radius: 5px;

  &:hover {
    background-color: var(--v2p-color-button-background-hover);
  }
}

.menu-item-icon {
  width: 18px;
  height: 18px;
}

.menu-item.active {
  background-color: var(--v2p-color-bg-content);
}

.content {
  overflow: auto;
  flex: 1;
  padding: 40px 50px;
  background-color: var(--v2p-color-bg-content);

  [data-content-key] {
    display: none;
  }
}

.title {
  font-size: 20px;
  font-weight: 600;
}

.backup-tip {
  margin-bottom: 20px;
  padding: 10px;
  font-size: 14px;
  background-color: var(--v2p-color-bg-block);
  border-radius: 5px;
}

.theme-select {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
  margin-bottom: 20px;

  .form-radio {
    position: relative;
    overflow: hidden;
    border: 1px solid var(--v2p-color-border);
    border-radius: 8px;

    &:hover {
      border-color: var(--v2p-color-border-darker);
    }
  }

  .combo {
    margin-left: 0;
  }

  @at-root {
    .theme-opt-input {
      position: absolute;
      bottom: 9px;
      left: 10px;
    }

    .theme-pv {
      font-size: 13px;

      .theme-pv-view {
        display: flex;
        flex-direction: column;
        aspect-ratio: 16 / 9;
        width: 260px;
        min-height: 100px;
        background-color: var(--v2p-color-background);

        .tpv-header {
          display: flex;
          gap: 10px;
          padding: 8px 10px;
          background-color: var(--v2p-color-bg-content);

          .tpv-header-item {
            width: 30px;
            height: 6px;
            background-color: var(--v2p-color-bg-avatar);
            border-radius: 999px;
          }
        }

        .tpv-main {
          display: flex;
          flex: 1;
          flex-direction: column;
          padding: 10px;

          .tpv-main-top {
            padding-bottom: 10px;

            .tpv-top-line {
              width: 30%;
              height: 8px;
              background-color: var(--v2p-color-bg-avatar);
              border-radius: 999px;
            }
          }

          .tpv-main-content {
            display: flex;
            flex: 1;
            gap: 10px;
            height: 80%;

            .tpv-main-left {
              position: relative;
              flex: 1;
              background-color: var(--v2p-color-bg-content);
              border-radius: 5px;

              .tpv-main-line {
                position: absolute;
                top: 15px;
                width: 100%;
                padding: 5px;
                background-color: var(--v2p-color-accent-50);

                .tpv-main-line-inner {
                  width: 25%;
                  height: 8px;
                  background-color: var(--v2p-color-accent-400);
                  border-radius: 999px;
                }
              }
            }

            .tpv-main-right {
              width: 25%;
              height: 65%;
              background-color: var(--v2p-color-bg-content);
              border-radius: 5px;
            }
          }
        }
      }

      .theme-pv-footer {
        padding: 5px 10px;

        .theme-type-name {
          padding-left: 20px;
        }
      }
    }
  }
}

#options-form {
  @at-root {
    .options-form-inner {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .form-item {
      display: flex;
      gap: 0 20px;
      align-items: center;
      font-size: 16px;

      & ~ & {
        margin-top: 5px;
      }

      > label {
        min-width: 120px;
      }
    }

    .form-checkbox {
      display: inline-flex;
      align-items: flex-start;

      input[type='checkbox'] {
        margin-top: 6px;
      }

      label {
        & ~ p {
          margin-top: 5px;
          font-size: 14px;
          color: var(--v2p-color-main-700);
        }
      }
    }

    .form-radio {
      display: flex;
      align-items: flex-start;

      input[type='radio'] {
        margin-top: 6px;
      }
    }

    .combo {
      margin-left: 10px;
    }

    label[for] {
      font-size: 15px;
      font-weight: bold;

      + p {
        font-size: 13px;
      }
    }

    hr {
      color: var(--v2p-color-divider);
    }
  }
}

fieldset {
  > legend {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  sup {
    cursor: help;
    margin-left: 5px;
    padding: 3px 4px;
    font-weight: normal;
    line-height: 1;
    color: var(--v2p-color-orange-400);
    background-color: var(--v2p-color-orange-100);
    border: 1px solid currentColor;
    border-radius: 4px;
  }
}

.actions {
  display: flex;
  justify-content: center;
}

.tags-tip {
  display: flex;
  gap: 10px;
  align-items: center;

  .usage {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-left: auto;
    color: var(--v2p-color-font-tertiary);

    .usage-help {
      cursor: help;
      position: relative;
      z-index: 1;
      width: 16px;
      height: 16px;
      border-radius: 4px;

      &:hover {
        .usage-popover {
          visibility: visible;
        }
      }

      .usage-popover {
        position: absolute;
        top: calc(100% + 5px);
        right: -5px;
        min-width: 180px;
        padding: 10px;
        font-size: 12px;
        visibility: hidden;
        background-color: var(--v2p-color-bg-content);
        border-radius: 5px;
        box-shadow: 0 0 10px var(--v2p-color-divider);

        .usage-popover-title {
          font-size: 13px;
          font-weight: 600;
        }

        .usage-popover-content {
          margin-top: 5px;
        }
      }
    }
  }
}

.tags-divider {
  margin: 10px 0 20px;
}

.tags-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 10px;
  list-style: none;

  @at-root {
    .tag-item {
      padding: 8px;
      border: 1px solid var(--box-border-color);
      border-radius: 0.4em;

      &:hover {
        .tag-item-tag-add {
          opacity: 1;
        }
      }
    }

    .tag-member-name {
      min-width: 100px;
      margin: 0 0 8px;
      font-weight: bold;

      a {
        display: flex;
        gap: 6px;
        align-items: center;

        &:hover {
          text-decoration: underline 1.5px;
          text-underline-offset: 0.46ex;
        }

        > img {
          width: 30px;
          height: 30px;
          background-color: var(--v2p-color-bg-block);
          border-radius: 5px;
        }
      }
    }

    .tag-item-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      font-size: 14px;
    }

    .tag-item-tag {
      cursor: pointer;
      position: relative;
      display: inline-flex;
      gap: 0 2px;
      align-items: center;
      justify-content: center;
      padding: 2px 6px;
      background-color: var(--v2p-color-bg-block);
      border-radius: 4px;

      &.tag-item-tag-add {
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.2s;
      }

      .tag-remove {
        cursor: pointer;
        position: absolute;
        z-index: 10;
        top: -0.8ex;
        right: -0.8ex;
        overflow: hidden;
        display: none;
        width: 14px;
        height: 14px;
        padding: 1px;
        color: var(--v2p-color-error);
        opacity: 0.7;
        background-color: var(--v2p-color-bg-error);
        border-radius: 9999px;

        &:hover {
          opacity: 1;
        }
      }

      &:hover .tag-remove {
        display: inline-block;
      }

      .tag-add {
        width: 1em;
        height: 1em;
      }
    }
  }
}

.tags-empty-block {
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  font-size: 14px;

  .empty-tip {
    padding: 20px 10px;
    text-align: center;
  }

  .empty-content {
    user-select: none;
    overflow: hidden;
    display: flex;
    gap: 10px;
    justify-content: center;
    padding: 20px;
    background-color: var(--v2p-color-bg-block);
    border-radius: 8px;
  }

  .avatar {
    position: relative;
    width: 48px;
    height: 48px;
    background-color: var(--v2p-color-main-300);
    border-radius: 5px;
  }

  .userinfo {
    position: relative;
    top: -5px;
    width: max-content;
    padding: 12px;
    background: var(--v2p-color-bg-widget);
    backdrop-filter: blur(16px);
    border: 1px solid var(--box-border-color);
    border-radius: 8px;
    box-shadow: var(--v2p-widget-shadow);
  }

  .userinfo-top {
    display: flex;
    gap: 0 20px;
    align-items: center;
  }

  .userinfo-bottom {
    margin-top: 20px;
  }

  .userinfo-btn {
    display: inline-flex;
    align-items: center;
    height: 28px;
    padding: 0 12px;
    font-family: inherit;
    font-weight: 500;
    color: var(--v2p-color-button-foreground);
    text-shadow: none;
    white-space: nowrap;
    background: var(--button-background-color);
    border-radius: 6px;
    box-shadow:
      0 1.8px 0 var(--box-background-hover-color),
      0 1.8px 0 var(--button-background-color);
  }

  .userinfo-avatar {
    width: 73px;
    height: 73px;
    background-color: var(--v2p-color-main-300);
    border-radius: 5px;
  }

  .userinfo-lines {
    display: flex;
    flex-direction: column;
    gap: 6px;

    div {
      height: 15px;
      background-color: var(--v2p-color-main-300);
    }
  }
}

.help-info {
  display: inline-block;
  width: 1em;
  height: 1em;

  i {
    width: 100%;
    height: 100%;
  }
}

@media screen and (width<=768px) {
  .side {
    width: 80px;

    .side-header-title {
      display: none;
    }

    .menu {
      padding: 12px;
    }

    .menu-item {
      justify-content: center;
    }

    .menu-item-title {
      display: none;
    }
  }

  .content {
    padding: 30px;
  }
}

@media screen and (width<=500px) {
  .side {
    width: 60px;

    .side-header {
      padding: 15px;
    }

    .menu {
      padding: 6px;
    }

    .menu-item {
      padding: 8px 4px;
    }
  }

  .content {
    padding: 15px;
  }

  .tags-list {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media screen and (width<=400px) {
  .side {
    width: 50px;

    .side-header {
      padding: 10px;
    }
  }
}
