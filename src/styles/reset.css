*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border-color: currentColor;
  border-style: solid;
  border-width: 0;
}

:where([hidden]:not([hidden='until-found'])) {
  display: none !important;
}

@supports not (min-block-size: 100dvb) {
  :where(html) {
    block-size: 100%;
  }
}

@media (prefers-reduced-motion: no-preference) {
  :where(html:focus-within) {
    scroll-behavior: smooth;
  }
}

:where(body) {
  font-family: system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  line-height: 1.5;
}

:where(input, button, textarea, select, textarea) {
  margin: 0;
  padding: 0;
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  color: inherit;
}

:where(textarea) {
  resize: vertical;
}

:where(button, label, select, summary, [role='button'], [role='option']) {
  cursor: pointer;
}

:where(button, [type='button'], [type='reset'], [type='submit']) {
  appearance: button;
  background-color: transparent;
  background-image: none;
}

:where(:disabled) {
  cursor: not-allowed;
}

:where(label:has(> input:disabled), label:has(+ input:disabled)) {
  cursor: not-allowed;
}

:where(h1, h2, h3, h4, h5, h6) {
  font-size: inherit;
  font-weight: inherit;
}

:where(a) {
  color: inherit;
  text-decoration: none;
}

:where(ul, ol) {
  margin: 0;
  padding: 0;
  list-style: none;
}

:where(fieldset) {
  margin: 0;
  padding: 0;
}

:where(legend) {
  padding: 0;
}

:where(img, svg, video, canvas, audio, iframe, embed, object) {
  display: block;
}

:where(img, picture, svg) {
  max-inline-size: 100%;
  block-size: auto;
}

:where(p, h1, h2, h3, h4, h5, h6) {
  overflow-wrap: break-word;
}

:where(h1, h2, h3) {
  line-height: calc(1em + 0.5rem);
}

:where(hr) {
  overflow: visible;
  height: 0;
  block-size: 0;
  color: inherit;
  border: none;
  border-block-start: 1px solid;
}

:where(:focus-visible) {
  outline: 2px solid var(--focus-color, Highlight);
  outline-offset: 2px;
}
