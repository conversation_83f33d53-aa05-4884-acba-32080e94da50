@use './share';

:root {
  color-scheme: light;

  &:has(#Wrapper.Night) {
    color-scheme: dark;

    #Top {
      background-color: transparent;
    }

    #search-container::before {
      background-image: url('/static/img/search_icon_light.png');
    }
  }

  html,
  body {
    min-height: 100vh;
  }
}

body {
  scrollbar-gutter: stable;
  overflow: overlay;

  &.v2p-theme-light-default {
    visibility: visible;
  }

  h1 {
    font-weight: bold;
  }

  a {
    cursor: default;
    text-decoration: none;

    &[href] {
      cursor: pointer;
    }

    &:hover {
      @include share.underline-text;
    }
  }

  pre {
    // 防止代码块不换行导致 Main 容器突破宽度。
    // .content: 1100px | #RightBar: 270px
    max-width: calc(1100px - 270px - 2 * var(--v2p-layout-column-gap) - 2 * 12px);
  }

  #Top {
    height: var(--v2p-nav-height);
    background-color: var(--v2p-color-bg-content);
    border: none;
  }

  #Bottom {
    color: var(--v2p-color-font-secondary);
    background-color: var(--v2p-color-bg-footer);
    border: none;

    .content {
      flex-direction: column; // 修复 v2ex.com/settings/invite 页脚布局错乱的问题。
    }
  }

  #Wrapper {
    background-color: inherit;
    background-image: none;

    &.Night {
      background-color: inherit;
      background-image: none;
    }

    .content {
      display: flex;
      gap: var(--v2p-layout-column-gap);
    }

    @at-root {
      // 站点帮助页的侧边菜单栏：https://www.v2ex.com/help/currency。
      #Navcol {
        .nav_item:hover {
          background-color: var(--box-background-hover-color);

          &:active {
            box-shadow: none;
          }
        }

        .nav_item_current {
          &:hover {
            color: var(--box-foreground-color);
            background-color: var(--v2p-color-bg-content);
          }

          &:active {
            box-shadow: none;
          }
        }
      }

      #Rightcol {
        #page-outline-title {
          background-color: var(--v2p-color-button-background-hover);
        }

        .page-outline-item:hover {
          background-color: var(--box-background-hover-color);
        }
      }
    }
  }

  #Leftbar {
    float: none;
    order: 1;
  }

  #Main {
    flex: 1;
    order: 2;
    max-width: 85vw;
    margin: 0;
  }

  #Rightbar {
    float: none;
    order: 3;
    margin-right: var(--v2p-layout-column-gap);
  }

  #search-container {
    height: 30px;
    margin: 0 30px;
    background-color: var(--v2p-color-bg-search);
    border: none;
    border-radius: 6px;

    &::before {
      top: 0;
      left: 4px;
      opacity: 0.6;
      background-size: 14px 14px;
      filter: none;
    }

    &.active {
      background-color: var(--v2p-color-bg-search-active);
    }

    #search-result {
      z-index: var(--zidx-serach);
      top: 42px;
      font-size: 14px;
      color: var(--v2p-color-font-secondary);
      background: var(--v2p-color-bg-widget);
      backdrop-filter: blur(16px);
      border: 1px solid var(--box-border-color);
      box-shadow: var(--v2p-widget-shadow);

      .fade {
        color: var(--v2p-color-font-secondary);
      }

      .search-item {
        font-weight: bold;
        color: var(--v2p-color-foreground);
        border-radius: 5px;

        &.active {
          color: var(--v2p-color-foreground);

          &.v2p-no-active {
            background-color: transparent;
          }
        }
      }
    }
  }

  .box {
    color: var(--box-foreground-color);
    background-color: var(--v2p-color-bg-content);
    border: none;
    border-radius: var(--box-border-radius);
    box-shadow: var(--v2p-box-shadow);

    .header {
      > h1 {
        font-size: 22px;
        font-weight: bold;
      }

      .gray {
        color: var(--color-gray);
      }
    }
  }

  .button {
    --button-hover-shadow: 0 1.8px 0 var(--button-border-color),
      0 1.8px 0 var(--button-background-color);

    &.normal,
    &.super {
      @include share.common-button;
    }

    &.special {
      --button-hover-shadow: 0 1.8px 0 var(--v2p-color-accent-200),
        0 1.8px 0 var(--v2p-color-accent-100);

      color: var(--v2p-color-accent-500);
      background: var(--v2p-color-accent-100);
      box-shadow: var(--button-hover-shadow);

      &:hover {
        &,
        &:enabled {
          color: var(--v2p-color-accent-600);
          background: var(--v2p-color-accent-100);
          border: none;
          box-shadow: var(--button-hover-shadow);
        }
      }
    }

    a {
      color: inherit;
      text-decoration: none;
    }
  }

  /* 帖主徽章 & 管理员徽章 & 自己徽章 */
  .badge {
    user-select: none;
    padding: 2px 5px;
    font-weight: bold;
    border: 1px solid var(--v2p-color-accent-400);

    &:first-child {
      border: 1px solid var(--v2p-color-accent-400);
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }

    &:last-child {
      border: 1px solid var(--v2p-color-accent-400);
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }

    &.op {
      color: var(--v2p-color-accent-500);
      background-color: var(--v2p-color-accent-50);
    }

    &.mod {
      color: var(--v2p-color-bg-content);
      background-color: var(--v2p-color-accent-400);
    }

    &.you {
      color: var(--v2p-color-orange-400);
      background-color: var(--v2p-color-orange-50);
      border: 1px solid var(--v2p-color-orange-400);
    }

    &.mini {
      height: 1.2em;
      padding: 0 3px;
      font-size: 12px;
      font-weight: normal;
      line-height: 1;
    }
  }

  a.node:is(:active, :link, :visited) {
    padding: 5px 6px;
    font-size: 13px;
    color: var(--v2p-color-font-secondary);
    background-color: var(--v2p-color-bg-block);
    border-radius: 4px;

    &:hover {
      color: var(--v2p-color-font-tertiary);
      background-color: var(--v2p-color-button-background-hover);
    }
  }

  .outdated {
    font-size: 12px;
    border-color: var(--v2p-color-border);
    border-bottom: none;
  }

  :is(.page_normal, .page_current) {
    &:is(:link, :visited) {
      user-select: none;
      padding: 6px 9px;
      font-size: 14px;
      border: none;
      border-radius: 4px;
    }
  }

  .page_normal:is(:link, :visited) {
    font-weight: 500;
    background-color: var(--v2p-color-bg-content);
    box-shadow: 0 2px 2px var(--box-background-hover-color);
    transition: transform 0.25s;

    &:hover {
      transform: scale(1.1) translateY(-2px);
    }
  }

  .page_current:is(:link, :visited) {
    pointer-events: none;
    font-weight: bold;
    background-color: var(--box-background-hover-color);
    box-shadow: none;
  }

  .page_input {
    display: none;
  }

  .dock_area {
    margin: 12px 0;
    background: var(--v2p-color-bg-block);
  }

  .member-activity-bar {
    background-color: var(--v2p-color-divider);

    .member-activity-start {
      background-color: var(--v2p-color-accent-200);
    }

    .member-activity-fourth {
      background-color: var(--v2p-color-accent-400);
    }

    .member-activity-half {
      background-color: var(--v2p-color-accent-500);
    }

    .member-activity-almost {
      background-color: var(--v2p-color-accent-600);
    }

    .member-activity-done {
      background-color: var(--v2p-color-orange-400);
    }
  }

  .online {
    user-select: none;
    padding: 6px 8px;
    font-size: 13px;
    color: var(--v2p-color-bg-content);
    background: var(--v2p-color-accent-400);
    border-radius: 5px;
  }

  #topic_supplement {
    @include share.input(550px) {
      overflow-y: auto;
    }
  }

  .item_hot_topic_title {
    --offset: 2.4px;

    @include share.line-clamp(2);

    position: relative;
    padding: var(--offset) 0;
    text-shadow: none;

    > a:hover {
      text-underline-offset: var(--offset);
    }
  }

  form {
    textarea#topic_title {
      @include share.input(75px);
    }

    #topic_title {
      @include share.input(30px);
    }

    #topic_content {
      @include share.input(120px);
    }

    // MARK: 记事本演示。
    // 记事本页面链接：https://www.v2ex.com/notes
    &[action^='/notes'] {
      .cell {
        background-color: transparent !important;
      }
    }
  }

  // MARK: 主题编辑内容的语法。
  #syntax-selector {
    .radio-group {
      padding: 3px;
      background-color: var(--v2p-color-background);

      & > input[type='radio'] {
        &:checked + label {
          background-color: var(--v2p-color-accent-100);
        }

        + label {
          cursor: pointer;
          font-size: 13px;
        }
      }
    }

    label {
      color: var(--v2p-color-foreground);
    }
  }

  // 提醒系统里的回复时间。
  .snow {
    color: var(--v2p-color-font-quaternary);
  }

  // 新消息提醒。
  .orange-dot {
    background: var(--v2p-color-orange-400);
  }

  // 输入框：https://www.v2ex.com/notes ｜ https://www.v2ex.com/t
  .alt {
    background-color: var(--v2p-color-bg-input);
    border: 1px solid var(--button-border-color);
  }

  // https://www.v2ex.com/i/about
  a.btn_hero {
    border-color: var(--v2p-color-foreground);

    &:hover {
      background-color: var(--v2p-color-foreground);
    }
  }

  .cell_ops {
    background-color: transparent;
  }

  :is(
      .topic_content,
      .reply_content,
      .v2p-topic-preview-content,
      .v2p-topic-preview-addons,
      .v2p-reply-preview,
      .markdown_body
    ) {
    a[href^='http'] {
      @include share.link;

      color: currentColor;
      background-color: var(--v2p-color-bg-link);

      &:hover {
        background-color: var(--v2p-color-bg-link-hover);
      }

      &:has(> .embedded_image) {
        background-color: transparent;
      }
    }

    // MARK: 站内链接。
    a[href*='v2ex.com/t'],
    a[href^='/'] {
      @include share.link;

      color: var(--v2p-color-accent-500);
      background-color: var(--v2p-color-accent-50);

      &:hover {
        color: var(--v2p-color-accent-500);
        background-color: var(--v2p-color-accent-50);
      }
    }
  }

  // MARK: 主题内容编辑器。
  .CodeMirror {
    color: currentColor;
    background-color: var(--v2p-color-bg-input);

    &.CodeMirror-focused {
      background-color: var(--v2p-color-bg-content);
    }

    .CodeMirror-gutters {
      padding-right: 5px;
      background-color: var(--v2p-color-bg-input);
      border-right: 1px solid var(--v2p-color-border-darker);
    }

    .CodeMirror-linenumber {
      color: var(--v2p-color-font-quaternary);
    }

    .CodeMirror-cursors {
      background-color: var(--v2p-color-foreground);
    }
  }

  .cm-s-one-dark {
    background-color: var(--v2p-color-bg-input);
  }

  #workspace {
    overflow: hidden;
    border: 1px solid var(--button-border-color);
    border-radius: 8px;
  }

  // 选择主题节点的下拉选择框。
  .select2-container {
    width: 200px !important;

    .select2-selection {
      background-color: var(--v2p-color-background);
      border: 1px solid var(--v2p-color-border);

      .select2-selection__rendered,
      .select2-selection__placeholder {
        color: var(--v2p-color-foreground);
      }
    }

    .select2-dropdown {
      @include share.popup;

      transform: translateY(5px);

      .select2-search {
        padding: 5px;

        .select2-search__field {
          padding: 6px 4px;
          background-color: transparent;
          border: 1px solid var(--v2p-color-border);
          border-radius: 4px;

          &:focus-visible {
            border-color: var(--v2p-color-font-quaternary);
            outline: none;
          }
        }
      }

      .select2-results > .select2-results__options {
        padding: 5px;
      }

      .select2-container--default .select2-results__option--selected {
        color: currentColor;
        background-color: var(--v2p-color-accent-100);
      }
    }

    .select2-results__option {
      border-radius: 4px;
    }

    .select2-results__option--highlighted.select2-results__option--selectable {
      color: currentColor;
      background-color: var(--v2p-color-main-200);
    }

    .select2-results__option--selected {
      color: currentColor !important;
      background-color: var(--v2p-color-accent-100) !important;
    }
  }

  // 提示回复没有内容。
  .problem {
    color: currentColor;
    color: var(--v2p-color-orange-400);
    background-color: var(--v2p-color-orange-50);
    border-color: var(--v2p-color-orange-400);
    border-bottom: none;
  }

  // MARK: 正文内容的表格。
  .markdown_body {
    table {
      border-top: 1px solid var(--v2p-color-border-darker);
      box-shadow: none;

      tr {
        th,
        td {
          border: 1px solid var(--v2p-color-border-darker);
        }
      }

      tr:nth-child(2n) {
        background-color: var(--box-background-alt-color);
      }
    }

    blockquote {
      margin-inline: 0;
      padding: 0 1em;
      color: var(--v2p-color-font-tertiary);
      border-left: 3px solid var(--v2p-color-border);
    }
  }

  // 个人页的链接。
  .social_label:is(:link, :visited, :active) {
    background-color: var(--v2p-color-button-background);
    border-radius: var(--box-border-radius);
    box-shadow: none;

    &:hover {
      background-color: var(--v2p-color-button-background-hover);
    }
  }

  .green {
    color: var(--v2p-color-accent-500);
  }

  .message {
    color: var(--v2p-color-orange-400);
    background-color: var(--v2p-color-orange-50);
    border: none;
  }

  // 账户余额。
  .balance_area,
  a.balance_area:is(:link, :visited) {
    display: inline-flex;
    gap: 3px;
    align-items: center;
    font-weight: 600;
    color: var(--v2p-color-foreground);
    text-shadow: none;
    background: var(--v2p-color-button-background);

    &:hover {
      background: var(--v2p-color-button-background-hover);
    }
  }

  :is(.subtle) {
    background-color: var(--v2p-color-bg-subtle);
    border-left: 3px solid var(--v2p-color-accent-200);

    .topic_content {
      font-size: 15px;
    }
  }

  // 设置页的开关。
  .onoffswitch label .frame::before {
    color: #fff;
    background-color: var(--v2p-color-accent-400);
  }

  .onoffswitch label .frame::after {
    color: var(--v2p-color-font-secondary);
    background-color: var(--v2p-color-bg-search);
  }

  select {
    @include share.select-box;

    padding: 4px 6px;
  }

  // MARK: 各种输入框。
  .ml,
  .mle,
  .mll,
  .sl,
  .sll,
  .sls {
    color: var(--v2p-color-foreground);
    background-color: var(--v2p-color-bg-content);
    border-color: var(--v2p-color-input-border);

    &:focus {
      border-color: var(--v2p-color-input-border);
    }
  }

  input,
  select,
  textarea {
    color: var(--v2p-color-foreground);
  }

  /* ------ 浏览器窗口滚动条 ------ */
  // Webkit
  &::-webkit-scrollbar-track {
    background-color: var(--v2p-scrollbar-background-color);
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--v2p-color-foreground);
  }

  // Firefox
  scrollbar-color: var(--v2p-color-foreground) var(--v2p-scrollbar-background-color);
}

/* ====== 帖子标签 ====== */

.box {
  .tag {
    &::before {
      color: var(--v2p-color-font-secondary);
    }

    &:link,
    &:visited {
      font-size: 12px;
      color: var(--v2p-color-font-secondary);
      background-color: var(--v2p-color-main-100);
      border-radius: 5px;
    }

    > li {
      opacity: 0.6;
    }
  }
}

/* ------ 帖子标签 ------ */

#Top {
  .content {
    height: 100%;
  }

  .site-nav {
    height: 100%;
    padding: 0;
  }

  .tools {
    display: flex;
    gap: 8px 14px;
    align-items: center;
    justify-content: flex-end;
    font-size: 14px;
    font-weight: 400;

    .top {
      height: 26px;
      padding: 0 6px;
      line-height: 26px;
      color: var(--v2p-color-button-foreground);
      white-space: nowrap;
      border-radius: 4px;

      &:hover {
        color: var(--v2p-color-foreground);
      }

      &:not(.v2p-hover-btn):hover {
        background-color: var(--v2p-color-button-background-hover);
      }
    }

    * {
      margin-left: 0;
    }
  }
}

/* ================================================================ */

#Main {
  .box {
    overflow: auto;
    padding: 0 12px;

    &.node-header {
      > .cell {
        margin: 0 -12px;
      }
    }

    .cell {
      padding: var(--v2p-tp-item-x) var(--v2p-tp-item-y);
      background-image: none !important;
    }

    .cell_ops {
      padding: 15px 5px;
    }

    // 主题编辑器样式：
    &:has(form[action='/write']) {
      .cell {
        &:nth-child(1),
        &:nth-child(2) {
          border: none;
        }

        &:has(#syntax-selector) {
          padding: 8px 0 !important;

          .tab-alt-container {
            gap: 0 8px;
          }

          .tab-alt {
            padding: 4px 2px;
            border-bottom-width: 2px;
            transition: none;

            &:not(.active):hover {
              border-color: transparent;
            }
          }
        }

        &#preview {
          padding: 2px 5px;
        }
      }
    }
  }

  /* ====== 帖子操作按钮 ====== */
  .topic_buttons {
    display: flex;
    flex-wrap: wrap;
    column-gap: 5px;
    align-items: center;
    padding: 8px 0;
    background: none;

    .topic_stats {
      float: none;
      flex: 1;
      order: 99;
      margin-left: 10px;
      padding: 0 !important;
      font-size: 12px;
      text-shadow: none;
    }

    .topic_thanked {
      font-size: 12px;
    }

    a.tb:link {
      display: flex;
      flex-direction: row-reverse;
      column-gap: 5px;
      align-items: center;
      padding: 5px;
      text-shadow: none;
      white-space: nowrap;
      background: none;
      border-radius: 4px;

      &:not(.v2p-hover-btn) {
        color: var(--v2p-color-font-secondary);
      }

      &:hover:not(.v2p-hover-btn) {
        color: currentColor;
        background: var(--v2p-color-bg-block);
      }
    }
  }

  .vote:link {
    color: var(--v2p-color-font-tertiary);
    border-color: var(--v2p-color-border-darker);
    border-radius: 5px;

    &:hover {
      box-shadow: 0 2px 2px var(--v2p-color-main-200);
    }
  }

  .cell {
    @mixin count {
      user-select: none;
      display: inline-block;
      padding: 5px 10px;
      font-size: 12px;
      font-weight: 400;
      white-space: nowrap;
      border-radius: 5px;
    }

    .topic-link {
      color: var(--v2p-color-foreground);
      text-decoration: none;

      &:visited {
        color: var(--v2p-color-link-visited);
      }
    }

    .topic_info {
      pointer-events: none;
      user-select: none;
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      &::after {
        content: '';
        position: absolute;
        z-index: 1;
        inset: 0 0 -6px;
        background-color: var(--v2p-color-bg-content);
      }

      .votes,
      .node,
      strong:first-of-type,
      span:first-of-type,
      .v2p-topic-ignore-btn {
        pointer-events: auto;
        position: relative;
        z-index: 2;
      }

      a[href^='/member'] {
        font-weight: 500;
        color: var(--v2p-color-font-tertiary);
        background-color: transparent;
      }
    }

    .count_livid {
      @include count;

      color: var(--v2p-color-button-foreground);
      background-color: var(--v2p-color-bg-block);
    }

    .count_orange {
      @include count;

      font-weight: bold;
      color: var(--v2p-color-bg-block);
      background-color: var(--v2p-color-orange-400);
    }

    .item_title {
      .topic-link {
        font-size: 15px;
        font-weight: 600;
      }
    }
  }

  .cell.item {
    tr > td:nth-child(2) {
      width: 30px;
    }
  }

  .box,
  .v2p-modal-content {
    > .cell[id^='r']:not(:has(.cell[id^='r'])) .reply_content {
      padding-bottom: 0;
    }
  }

  .cell[id^='r'] {
    --bg-reply: var(--v2p-color-bg-content);

    background-color: var(--bg-reply);

    &:not(:has(+ .cell[id^='r'])) {
      border-bottom: none; // 去除最后一条回复的底边框。
    }

    &:hover {
      > table {
        td:last-of-type {
          .fr {
            a {
              opacity: 1;
            }
          }
        }
      }
    }

    .ago {
      font-size: 12px;
      color: var(--v2p-color-font-quaternary);
      white-space: nowrap;
    }

    .reply_content {
      padding-bottom: 10px;
      line-height: 1.5; // 回复内容行距可以紧凑一点。
    }

    > table {
      &:first-of-type td {
        &:first-of-type {
          width: 40px;

          .avatar {
            display: inline-block;
            aspect-ratio: 1;
            width: 40px !important;
            height: 40px !important;
            background-color: var(--v2p-color-bg-avatar);
            border-radius: 5px;
          }
        }
      }

      /* 嵌套的回复 */
      ~ .cell[id^='r'] {
        --bg-reply: var(--v2p-color-bg-reply);

        position: relative;
        z-index: var(--zidx-expand-btn);
        padding: var(--v2p-tp-nested-pd) 0 0 var(--v2p-tp-nested-pd);
        border: none;
        border-radius: 0;
        box-shadow: -2.4px 0 var(--v2p-color-border-darker);

        /* 多层嵌套 */
        .cell[id^='r'] {
          padding: 0;
          box-shadow: none;

          &.v2p-indent {
            padding-left: var(--v2p-tp-nested-pd); /* 取消靠左对齐，逐层缩进 */
            border-left: 1px solid var(--v2p-color-border-darker);
          }
        }

        tr td {
          &:first-of-type {
            width: 25px;

            .avatar {
              width: 25px !important;
              height: 25px !important;
              border-radius: 4px;
            }
          }

          /* 回复人的昵称 */
          &:nth-child(3) strong a {
            font-size: 13px;
          }
        }

        /* 嵌套回复的内容 */
        .reply_content {
          padding-right: 5px;
        }
      }

      td {
        &:nth-of-type(2) {
          /* 头像与右侧的距离 */
          width: 15px;
        }

        &:last-of-type {
          a.dark {
            color: var(--v2p-color-font-secondary);
            text-decoration: none;

            &:hover {
              text-decoration: none;
            }
          }

          .fr {
            user-select: none;
            position: relative;
            top: -3px;

            a {
              opacity: 0;
            }

            & + .sep3 {
              height: 0;
            }
          }
        }
      }
    }

    &:last-of-type {
      border: none;
    }

    .no {
      user-select: none;
      position: relative;
      top: -4px;
      padding: 5px 10px;
      font-size: 12px;
      color: var(--v2p-color-cell-num);
      background-color: transparent;
      border-radius: 5px;
    }
  }

  #Tabs {
    user-select: none;
    position: sticky;
    z-index: var(--zidx-tabs);
    top: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 5px 3px;
    align-items: center;
    padding: var(--v2p-tp-tabs-pd);
    background-color: var(--v2p-color-bg-content);
    border-bottom: 1px solid var(--box-border-color);

    .tab_current {
      margin-right: 0;
    }

    .tab {
      margin: 0;

      &.v2p-hover-btn {
        &::before {
          inset: 0;
        }
      }
    }
  }

  #SecondaryTabs {
    padding: 10px;
    background-color: var(--v2p-color-bg-tabs);
    border-radius: 5px;

    a {
      color: var(--v2p-color-tabs);
    }
  }

  .topic_content,
  .reply_content {
    @include share.text-content;

    color: currentColor;

    // 被 @ 的用户
    a[href^='/member'] {
      position: relative;
      bottom: 1px;
      font-size: 13px;
      color: var(--v2p-color-font-tertiary);
      text-decoration: underline;
      text-underline-offset: 0.4ex;
      background-color: transparent;
    }
  }

  .thank_area {
    font-size: 12px;
  }

  .tab {
    user-select: none;
    color: var(--v2p-color-foreground);
    background-color: transparent;

    &:not(.v2p-hover-btn):hover {
      background-color: var(--v2p-color-bg-block);
    }
  }

  .tab_current {
    user-select: none;
    color: var(--v2p-color-bg-content);
    background-color: var(--v2p-color-foreground);
  }

  #reply-box {
    &.reply-box-sticky {
      z-index: var(--zidx-reply-box);
      bottom: 20px;
      overflow: visible;
      margin: 0 -10px;
      padding: 0 22px;
      border: none;
      border-radius: var(--box-border-radius);
      outline: 2px solid var(--v2p-color-border);
    }

    .flex-one-row:last-of-type {
      flex-direction: row-reverse;
      gap: 10px;
      justify-content: flex-start;

      .gray {
        margin-right: auto;
      }
    }

    > .cell {
      font-size: 12px;

      &.flex-one-row {
        min-height: 45px;
        padding: 0 10px;
        border: none;
      }

      &.flex-row-end {
        padding: 12px 10px;
        border: none;
      }

      &:has(form) {
        padding-top: 0;
      }
    }
  }

  #no-comments-yet {
    color: var(--color-gray);
    border-color: var(--color-gray);
  }

  #notifications {
    .cell[id^='n'] {
      &:hover {
        .node {
          opacity: 1;
        }
      }

      .node {
        opacity: 0;
      }

      .payload {
        color: var(--v2p-color-foreground);
        background-color: var(--v2p-color-bg-block);
      }

      .topic-link:visited {
        color: var(--v2p-color-font-quaternary);
      }
    }
  }

  .cell_tabs {
    .cell_tab_current {
      font-weight: bold;
      border-color: var(--v2p-color-foreground);
    }

    .cell_tab {
      color: var(--v2p-color-foreground);

      &:hover {
        border-color: var(--v2p-color-border-darker);
      }
    }
  }
}

/* ================================================================ */

#Rightbar {
  .cell:has(.light-toggle) {
    font-size: 13px;
  }

  a.dark:is(:link, :active, :visited, :hover) {
    color: var(--v2p-color-font-tertiary);

    &:hover {
      color: var(--v2p-color-font-secondary);
    }
  }
}

/* ================================================================ */

#Bottom {
  position: sticky;
  top: 100%;

  a.dark {
    font-size: 13px;
    font-weight: 400;

    &:is(:link, :active, :visited, :hover) {
      color: var(--v2p-color-font-tertiary);
    }
  }
}

// 广告：
.wwads-cn {
  border: none !important;
  box-shadow: none !important;
}

// 广告：
.box:has(a[href^='/advertise']) {
  overflow: hidden;
  border: none !important;
  box-shadow: none !important;

  .sidebar_compliance {
    background-color: var(--v2p-color-bg-block);
  }
}
