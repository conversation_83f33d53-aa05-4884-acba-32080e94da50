@use './share';

:root {
  --common-padding: 20px;
}

html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  width: 500px;
  height: 600px;
  font-size: 12px;
  color: var(--v2p-color-foreground);
  background-color: var(--v2p-color-bg-content);

  .lucide-sun {
    display: none;
  }

  .lucide-moon-star {
    display: block;
  }
}

body.v2p-theme-dark-default {
  color-scheme: dark;
  border: 2px solid var(--v2p-color-foreground);

  .lucide-sun {
    display: block;
  }

  .lucide-moon-star {
    display: none;
  }
}

main {
  --nav-height: 58px;

  position: relative;
  overflow: hidden;
  height: 100%;
  background-color: var(--v2p-color-bg-content);

  > nav {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    display: flex;
    align-items: center;
    height: var(--nav-height);
    padding: 0 10px;
    background-color: var(--v2p-color-bg-content);
  }

  > section {
    box-sizing: border-box;
    height: 100%;
    padding-top: var(--nav-height);
  }
}

button {
  &:disabled {
    opacity: 0.8;
  }
}

.action-btn {
  display: inline-flex;
  gap: 0 6px;
  align-items: center;
  justify-content: center;
  padding: 5px 8px;
  color: var(--v2p-color-foreground);
  text-decoration: none;
  background-color: var(--v2p-color-button-background);
  border-radius: 5px;

  &:hover {
    text-decoration: none;
    background-color: var(--v2p-color-button-background-hover);
  }

  .action-icon {
    display: inline-block;
    width: 15px;
    height: 15px;
  }
}

.tabs {
  display: flex;
  gap: 0 3px;
  align-items: center;
  width: 100%;
  padding: 4px 6px;
  font-size: 14px;
  background-color: var(--v2p-color-bg-tabs);
  border-radius: 5px;

  li[data-target] {
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 30px;
    padding: 0 10px;
    letter-spacing: 1px;
    border-radius: 3px;

    &:hover {
      background-color: var(--v2p-color-button-background-hover);
    }

    &.active {
      color: var(--v2p-color-background);
      background-color: var(--v2p-color-foreground);
    }
  }

  .tabs-right {
    margin-left: auto;
  }
}

.tab-header {
  padding: var(--common-padding);
  padding-bottom: 0;
}

.tab-contents {
  height: 100%;
}

.tab-content {
  overflow: hidden auto;
  display: none;
  height: 100%;

  &.active {
    display: block;
  }

  .tab-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 20px;

    .loading {
      position: relative;
      top: -100px;
      right: -13px;
      width: 50px;
    }
  }

  .list {
    padding: 0;
  }

  .topic-item {
    position: relative;

    > a {
      display: block;
      padding: 20px 15px;
      color: currentColor;
      text-decoration: none;

      &:hover {
        background-color: var(--v2p-color-bg-link);
      }

      .title {
        @include share.line-clamp(2);

        margin-bottom: 6px;
        font-size: 16px;
        font-weight: bold;
      }

      .content {
        @include share.line-clamp(3);

        padding: 0;
        font-size: 13px;
        color: var(--v2p-color-font-secondary);
      }
    }

    .topic-item-actions {
      position: absolute;
      top: 0;
      right: 0;
      display: inline-flex;
      gap: 10px;
      padding: 4px 8px;
      opacity: 0;
    }

    &:hover {
      .topic-item-actions {
        opacity: 1;
      }
    }

    .topic-item-action-remove {
      padding: 1px 6px;
      color: var(--v2p-color-foreground);
      background-color: var(--v2p-color-bg-block-darker);
      border-radius: 2px;
      box-shadow: var(--v2p-box-shadow);

      &:hover {
        color: #ef4444;
        background-color: #fee2e2;
      }
    }
  }

  .notice-item {
    padding: 15px 12px;
    border-bottom: 1px dashed var(--v2p-color-border);

    .notice {
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      > a {
        margin: 0 5px;

        &:hover {
          text-decoration: underline dashed;
          text-underline-offset: 0.4ex;
        }
      }
    }

    .payload {
      margin-top: 5px;
      padding: 5px;
      background-color: var(--v2p-color-bg-block);
      border-radius: 5px;
    }
  }

  .settings {
    padding: var(--common-padding);

    @at-root {
      h2 {
        margin-bottom: 8px;
        font-size: 15px;
        font-weight: bold;

        sup {
          margin-left: 5px;
          padding: 2px 4px;
          font-weight: normal;
          line-height: 1;
          color: var(--v2p-color-orange-400);
          background-color: var(--v2p-color-orange-100);
          border: 1px solid currentColor;
          border-radius: 4px;
        }
      }

      .settings-top {
        display: flex;
        align-items: center;
      }

      .description {
        padding-top: 20px;

        .description-title {
          font-size: 15px;
          font-weight: 500;
        }

        .description-icon {
          display: inline-block;
          width: 16px;
          height: 16px;
          margin: 0 10px 0 3px;
        }

        ul {
          padding: 10px 0;

          li {
            display: flex;
            align-items: center;
            font-size: 13px;

            & ~ li {
              margin-top: 6px;
            }

            > {
              p {
                flex: 1;
              }
            }
          }
        }

        .learn-more {
          padding: 10px 0;
          font-size: 14px;
        }
      }
    }

    .pat-input {
      position: relative;
      overflow: hidden;
      display: flex;
      gap: 0 10px;
      align-items: center;
      padding: 0 5px 0 0;
      background-color: var(--v2p-color-bg-block);
      border: 1px solid var(--v2p-color-border);
      border-radius: 6px;

      &:has(.has-value) {
        background-color: transparent;
        border: 1px solid var(--v2p-color-font-tertiary);
      }

      #pat {
        flex: 1;
        padding: 10px 8px;
        background-color: transparent;
        border: none;
        outline: none;

        &::placeholder {
          color: var(--v2p-color-font-secondary);
        }

        &.has-value {
          opacity: 0.6;
          filter: blur(6px);

          &:focus {
            opacity: 1;
            filter: none;
          }
        }
      }

      .submit-btn {
        display: flex;
        align-items: center;
        height: 100%;
        padding: 5px 10px;
        font-size: 12px;
        color: var(--v2p-color-background);
        background-color: var(--v2p-color-foreground);
        border: none;
        border-radius: 5px;
      }
    }

    .form-item {
      display: flex;
      gap: 0 20px;
      align-items: center;
      font-size: 13px;

      & ~ .form-item {
        margin-top: 5px;
      }

      > label {
        min-width: 95px;
        font-size: inherit;
        font-weight: normal;
        color: var(--v2p-color-font-secondary);
      }

      > input {
        background-color: transparent;
      }
    }
  }
}

input {
  outline: none;
}

hr {
  margin: 15px 0;
  border: none;
  border-top: 1px solid var(--v2p-color-divider);
}

.message-actions {
  display: flex;
}

.storage-data-bar {
  display: inline-flex;
  gap: 0 20px;
  align-items: center;
  margin-top: 10px;
  padding: 5px 8px;
  color: var(--v2p-color-foreground);
  background-color: var(--v2p-color-bg-block);
  border-radius: 5px;

  @at-root {
    #clear-storage,
    #sync-settings {
      padding: 0 5px;
      background-color: var(--v2p-color-button-background-hover);
      border-radius: 3px;
      transition:
        background-color 0.2s,
        color 0.2s;
    }

    #clear-storage {
      &:hover {
        color: var(--v2p-color-error);
        background-color: var(--v2p-color-bg-error);
      }
    }

    #sync-settings {
      &:hover {
        color: var(--v2p-color-accent-600);
        background-color: var(--v2p-color-accent-100);
      }
    }
  }
}

.fetch-error {
  padding: 50px 0;
  text-align: center;
}

.tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;

  > p {
    margin-bottom: 10px;
  }
}

.button {
  &.normal {
    @include share.common-button;
  }
}

.details {
  padding: 5px 0;

  summary:hover {
    .summary-text {
      text-decoration: underline dashed;
      text-underline-offset: var(--v2p-underline-offset);
    }
  }

  @at-root {
    .details-content {
      padding: 8px 0 0;
      color: var(--v2p-color-font-secondary);
    }
  }
}

a {
  &:hover {
    @include share.underline-text;
  }

  &.link {
    text-decoration: underline dashed;
    text-underline-offset: var(--v2p-underline-offset);

    &:hover {
      text-decoration: underline;
      background-color: var(--v2p-color-bg-link-hover);
    }
  }
}

.features {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: auto;
  gap: 20px;
  padding: 10px;

  .feature {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
    background-color: var(--v2p-color-bg-block);
  }

  .feature-check-in {
    grid-column: 2 / span 2;
  }

  .feature-title {
    font-size: 15px;
    font-weight: bold;
  }

  .feature-content {
    margin-top: 10px;
  }
}

.empty-block {
  padding: 40px 20px;
  font-size: 14px;
  text-align: center;
}

.empty-emoji {
  font-size: 20px;
}

.empty-text {
  margin-top: 10px;

  & ~ & {
    margin-top: 5px;
  }
}

.empty-img-block {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.menu-wrapper {
  overflow: hidden;
  width: 320px;
  margin-top: 20px;
  background-color: var(--v2p-color-bg-block);
  border-radius: 8px;
}

.fake-menu {
  --width: 160px;
  --gap: 8px;
  --round: 6px;

  pointer-events: none;
  user-select: none;
  position: relative;
  top: -22px;
  left: 28px;
  width: var(--width);
  padding: 0 10px;
  background-color: var(--v2p-color-bg-content);
  border: 1px solid var(--v2p-color-border-darker);
  border-radius: var(--round);
  box-shadow: var(--v2p-widget-shadow);

  &.menu-root {
    position: relative;
    right: 50px;
  }

  .fake-item {
    width: 100%;
    height: 1em;
    margin: var(--gap) 0;
    background-color: var(--v2p-color-bg-block);
    border-radius: 3px;
  }

  .active-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: var(--gap) -5px;
    padding: 1px 5px;
    font-size: 12px;
    background-color: var(--v2p-color-button-background-hover);
    border-radius: 4px;

    .fake-menu {
      position: absolute;
      top: -3px;
      left: 100%;
      width: calc(var(--width) - 40px);
    }

    .left {
      display: flex;
      align-items: center;
    }

    .logo {
      width: 16px;
      height: 16px;
      margin: 0 5px 0 3px;
      padding: 3px;
      color: white;
      background-color: var(--v2p-color-foreground);
      border-radius: 2px;
    }
  }

  .select-item {
    display: flex;
    justify-content: center;
    font-weight: normal;
    color: #fff;
    letter-spacing: 1px;
    background-color: var(--v2p-color-foreground);
  }

  .item-divider {
    height: 1px;
    background-color: var(--v2p-color-divider);
  }
}

.empty-img-caption {
  margin-top: 5px;
  font-size: 12px;
  color: var(--v2p-color-font-secondary);
}
