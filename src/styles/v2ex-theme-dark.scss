:root {
  @mixin dark-mode {
    // 基础色：
    --v2p-color-main-50: unset;
    --v2p-color-main-100: #2d333b;
    --v2p-color-main-200: #374151;
    --v2p-color-main-300: #374151;
    --v2p-color-main-350: #6b7280cc;
    --v2p-color-main-400: #6b7280;
    --v2p-color-main-500: #9ca3af;
    --v2p-color-main-600: #9ca3af;
    --v2p-color-main-700: #d1d5db;
    --v2p-color-main-800: #e5e7eb;
    --v2p-color-main-900: #111827;
    --v2p-color-main-950: #030712;
    --v2p-color-accent-50: #064e3b;
    --v2p-color-accent-100: #065f46;
    --v2p-color-accent-200: #047857;
    --v2p-color-accent-300: #059669;
    --v2p-color-accent-400: #10b981;
    --v2p-color-accent-500: #34d399;
    --v2p-color-accent-600: #6ee7b7;
    --v2p-color-orange-50: #593600;
    --v2p-color-orange-100: #9a3412;
    --v2p-color-orange-400: #fbe090;

    // ====
    --v2p-color-background: #000000;
    --v2p-color-foreground: #adbac7;
    --v2p-color-font-secondary: var(--v2p-color-main-600);

    // ==== 按钮 ====
    --v2p-color-button-background: #373e47;
    --v2p-color-button-foreground: var(--v2p-color-foreground);
    --v2p-color-button-background-hover: #444c56;
    --v2p-color-button-foreground-hover: var(--v2p-color-foreground);
    // ---- 按钮 ----

    // ==== 背景 ====
    --v2p-color-bg-content: #111111;
    --v2p-color-bg-subtle: rgb(6 78 59 / 30%);
    --v2p-color-bg-input: var(--v2p-color-background);
    --v2p-color-bg-search: var(--v2p-color-main-100);
    --v2p-color-bg-search-active: var(--v2p-color-main-200);
    --v2p-color-bg-widget: var(--v2p-color-bg-content);
    --v2p-color-bg-reply: var(--v2p-color-main-100);
    --v2p-color-bg-tooltip: var(--v2p-color-main-100);
    --v2p-color-bg-avatar: var(--v2p-color-main-300);
    --v2p-color-bg-block: #373e47;
    // ---- 背景 ----

    --v2p-color-heart: #ef4444;
    --v2p-color-heart-fill: #fca5a5;
    --v2p-color-mask: rgb(99 110 123 / 40%);
    --v2p-color-border: #444c56;
    --v2p-color-input-border: #444c56;
    --v2p-color-border-darker: #444c56;

    // ==== 阴影 ====
    --v2p-box-shadow: 0 0 0 1px var(--v2p-color-border);
    --v2p-toast-shadow: none;
    // ---- 阴影 ----

    // 滚动条
    --v2p-scrollbar-background-color: #1a1a1a;

    // V2EX 原有的 CSS 变量：
    --link-color: var(--v2p-color-foreground);
    --box-background-alt-color: var(--v2p-color-main-100);
    --box-background-hover-color: var(--v2p-color-main-300);
    --button-hover-color: var(--button-background-hover-color);
    --button-border-color: var(--v2p-color-border);
    --button-border-hover-color: #768390;

    visibility: visible;

    #Logo {
      background-image: url('https://www.v2ex.com/static/img/<EMAIL>');
    }

    ::selection {
      color: var(--v2p-color-background, #1c2128);
      background-color: var(--v2p-color-foreground, #adbac7);
    }

    img::selection {
      background-color: var(--v2p-color-foreground, #adbac7);
    }
  }

  body.v2p-theme-dark-default,
  .v2p-theme-dark-default,
  &[data-darkreader-scheme='dark'] body {
    @include dark-mode;
  }

  @supports selector(:has(*)) {
    body:has(#Wrapper.Night) {
      @include dark-mode;
    }
  }

  @supports not selector(:has(*)) {
    #Wrapper.Night {
      @include dark-mode;
    }
  }
}
