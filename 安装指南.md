# V2EX Polish 界面优化测试安装指南

## 方法一：直接从 WSL 加载（推荐）

1. 打开 Windows 文件资源管理器
2. 在地址栏输入：`\\wsl$\Ubuntu\root\Github\V2EX_Polish\extension`
   - 如果您的 WSL 发行版不是 Ubuntu，请替换为相应的名称（如 `\\wsl$\Debian` 等）
3. 打开 Chrome 浏览器
4. 在地址栏输入：`chrome://extensions/`
5. 开启右上角的"开发者模式"
6. 点击"加载已解压的扩展程序"
7. 选择步骤 2 中的 `extension` 文件夹

## 方法二：从 Windows 文件系统加载

如果方法一不工作，可以使用已复制到 Windows 的文件：

1. 打开 Chrome 浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `C:\temp\v2ex-polish-extension` 文件夹

## 测试界面优化效果

1. 安装扩展后，访问 [V2EX 网站](https://www.v2ex.com/)
2. 您应该能看到以下改进：

### 黑夜模式优化
- 点击页面右上角的主题切换按钮切换到黑夜模式
- 主背景色变为纯黑色 (#000000)
- 内容区域背景色为深黑色 (#111111)
- 滚动条背景色为深灰黑色 (#1a1a1a)

### 布局优化
- 帖子之间的间距更紧凑
- 右侧栏完全隐藏，主内容区域更宽敞
- 用户头像、未读提醒、创作按钮移至导航栏右上角
- 搜索框设计更现代化，交互体验更好

## 开发模式下的实时更新

如果您想在开发过程中实时看到更改：

1. 在 WSL 中运行监听命令：
   ```bash
   cd /root/Github/V2EX_Polish
   npm run watch
   ```

2. 每次修改样式文件后，在 Chrome 扩展页面点击扩展的"刷新"按钮

3. 刷新 V2EX 页面即可看到更改

## 故障排除

### 如果扩展无法加载：
- 确保 `manifest.json` 文件存在于扩展文件夹中
- 检查 Chrome 控制台是否有错误信息
- 尝试重新生成 manifest.json：
  ```bash
  cd /root/Github/V2EX_Polish
  npx tsx scripts/build-manifest.ts
  ```

### 如果图标无法显示：
- 检查浏览器控制台是否有图标相关错误
- 确保所有图标名称使用正确的大写格式（如 `Home`、`Bell`、`Settings` 等）
- 在扩展页面刷新扩展后重新访问 V2EX

### 如果样式没有生效：
- 确保样式文件已编译：
  ```bash
  npm run build:style
  ```
- 在 Chrome 扩展页面刷新扩展
- 清除浏览器缓存并刷新 V2EX 页面

### 如果 WSL 路径无法访问：
- 确保 WSL 正在运行
- 尝试在 Windows 命令提示符中运行：`wsl --list --running`
- 如果 WSL 未运行，启动它：`wsl`

## 修改内容总结

### 黑夜模式优化
- **主背景色**：`#1c2128` → `#000000`
- **内容背景色**：`#22272e` → `#111111`
- **滚动条背景色**：`#22303f` → `#1a1a1a`

### 布局改进
- **帖子间距**：从 20px 减少到 12px，更紧凑
- **右侧栏**：完全隐藏，释放更多空间
- **主内容区**：移除宽度限制，充分利用屏幕空间

### 导航栏优化
- **图标化导航**：所有导航项都使用图标替代文字，界面更简洁
- **完整导航功能**：首页、记事本、时间轴、设置、HODL、未读提醒、创作
- **圆形按钮设计**：所有按钮都是圆形，提供现代化视觉体验
- **用户头像**：圆形头像显示在右上角，点击进入用户页面
- **主题切换**：内置主题切换按钮，支持一键切换明暗主题
- **未读提醒**：带有红色徽章显示未读数量
- **暗夜模式特效**：按钮背景为纯黑色，悬停时变为白色，保持圆形
- **响应式设计**：在不同屏幕尺寸下自动适配

### 搜索框美化
- **现代设计**：圆角边框，更好的视觉效果
- **交互优化**：焦点状态、悬停效果
- **响应式**：适配不同屏幕尺寸

这些改进提供了更现代化、更高效的用户体验。
